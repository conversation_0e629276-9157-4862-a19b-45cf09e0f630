<script lang="ts">
	import type { Customer } from '$lib/types/customer';
	export let customer: Customer;
    import { t } from '$lib/stores/i18n';

    import { 
        Spinner, 
        Tooltip,
		Button,
        Dropdown,
        DropdownItem
    } from 'flowbite-svelte';
	
    import { 
        FolderOpenSolid,
        FileInvoiceSolid,
        ThumbsUpSolid,
        ShoppingBagSolid,
        RefreshOutline,
        FileCopyOutline,
        BookOpenOutline,
        DotsVerticalOutline,
		PaperPlaneSolid
    } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
  
    let userInput = '';
    let loading = false;
    let selectedReplyType = 'search';
    let selectedDocType = 'customer_support';
  
    let suggestions = [];
    let parsedSuggestions = [];
    let categorizedSuggestions = {};
    let errorMessage = '';
    let reply = '';
  
    let showSuccess = false;
    let successCounter = 0;
    let showNoResult = false;
    let noResultCounter = 0;
    let showError = false;
    let errorCounter = 0;

    let chatContainer: HTMLElement;
  
    import {
        parseRawSuggestion,
        convertNewlinesToBr,
        getSuggestionFields,
    } from '$lib/utils/guidance';


    // Function to handle typemode switching
    const handleTypeChange = (type) => {
        selectedReplyType = type;
        // Reset docType to default when switching to search
        if (type === 'search' && selectedDocType === 'all') {
            selectedDocType = 'customer_support';
        }
    };
  
    // Function to handle content type switching
    const handleDocTypeChange = (type) => {
        selectedDocType = type;
    };

    // Function to scroll to bottom of chat
    const scrollToBottom = () => {
        if (chatContainer) {
            setTimeout(() => {
            chatContainer.scrollTop = chatContainer.scrollHeight;
            }, 100);
        }
    };
  
    // Function to copy the reply to clipboard
    const copyToClipboard = async (text) => {
        try {
            await navigator.clipboard.writeText(text);
            showSuccess = true;
            successCounter = 3;
            // successTimeout();
        } catch (error) {
            console.error('Error copying text:', error);
            showError = true;
            errorCounter = 3;
            // errorTimeout();
        }
    };
  
    // Function to fetch data from API
    const fetchData = async (mode) => {
        suggestions = [];
        parsedSuggestions = [];
        categorizedSuggestions = {};
      
        try {
            loading = true;          
            const apiUrl = `/api/guidance/${mode}`;
            const request_body = {
                query: userInput,
                collection_name: selectedDocType,
                k: '3'
            };
    
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request_body)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                console.error('API Error:', errorData);
                errorMessage = errorData.detail || 'API Error: ' + response.statusText;
                showError = true;
                errorCounter = 3;
                //   errorTimeout();
                return;
            }
  
            const data = await response.json();
    
            if (data) {
                reply = data.reply || '';
                suggestions = data.docs ?? [];
                console.log('Suggestions:', suggestions);
                
                if (suggestions.length > 0) {
                    showSuccess = true;
                    successCounter = 2;
                    // successTimeout();
                } else {
                    showNoResult = true;
                    noResultCounter = 2;
                    // noResultTimeout();
                }
            } else {
                suggestions = [];
                errorMessage = 'No results found';
                showNoResult = true;
                noResultCounter = 3;
                // noResultTimeout();
            }

            // Parse suggestions
            suggestions.forEach((suggestion) => {
                parsedSuggestions.push({
                    category: selectedDocType,
                    ...parseRawSuggestion(suggestion)
                });
            });

            console.log('Parsed Suggestions:', parsedSuggestions);
  
            parsedSuggestions.forEach((suggestion) => {
            if (!(suggestion.category in categorizedSuggestions)) {
                categorizedSuggestions[suggestion.category] = [];
            }
            categorizedSuggestions[suggestion.category].push(suggestion);
            });
            console.log('categorizedSuggestions:', categorizedSuggestions);
    
        } catch (error) {
            console.error('Error fetching data:', error);
            suggestions = [];
            errorMessage = 'Error: ' + error.message;
            showError = true;
            errorCounter = 3;
            // errorTimeout();
        } finally {
            loading = false;
        }
    };
  
    // Function to handle sending a message
    const handleSend = async () => {
      if (!userInput.trim()) return;
  
      loading = true;
      errorMessage = '';
      
      try {
        if (selectedReplyType === 'search') {
            await fetchData('search');
        } else {
            await fetchData('smart_reply');
        }
        scrollToBottom();
      } catch (error) {
        console.error('Error processing request:', error);
        errorMessage = error.message || 'An error occurred';
        showError = true;
        errorCounter = 3;
        // errorTimeout();
      } finally {
        loading = false;
      }
    };
  
    // Function to handle quick action buttons
    const handleQuickAction = (action) => {
      userInput = action;
      handleSend();
    };

    // Function to restart conversation
    const restartConversation = () => {
      userInput = '';
      suggestions = [];
      parsedSuggestions = [];
      categorizedSuggestions = {};
      reply = '';
      errorMessage = '';
    };
  
    // Define quick actions
    const quickActions = [
      { id: 'cancel', label: 'ขอยกเลิกประกัน', category: 'customer_support' },
      { id: 'car', label: 'ขอประกันรถยนต์ชั้น2', category: 'product' },
      { id: 'process', label: 'สอบถามวิธีเคลม', category: 'customer_support' },
    ];

    // คำถามติดตามผล (Follow-up Questions)
    const followUpQuestions: string[] = [
        "เหตุใดจึงอยากยกเลิกประกันนี้?",
        "ต้องการยกเลิกทันทีหรือเมื่อสิ้นสุดรอบปีกรมธรรม์?",
        "มีปัญหาด้านเบี้ยประกันหรือความคุ้มครองใดที่กังวลอยู่หรือไม่?",
        "สนใจทางเลือกปรับลดเบี้ยหรือปรับทุนประกันแทนการยกเลิกหรือไม่?",
    ];

    const handleInsertToInput = (text: string) => {
        userInput = text;               // เติมลงช่องอินพุต
        chatContainer?.focus();         // โฟกัสช่องกรอกให้ผู้ใช้แก้ไขต่อ
    };

</script>
  
<div class="w-full h-full p-4 space-y-3 overflow-y-auto">
	<!-- Personalized Chatbot -->
	<div class="w-full p-6 mb-2 bg-white rounded-lg shadow-md">
		<div class="flex items-center justify-between mb-4">
			<div class="text-lg font-medium text-gray-700">{t('personalized_chatbot')}</div>
			
			<!-- Dropdown Menu -->
			<div class="relative">
				<button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
					<DotsVerticalOutline class="h-5 w-5 text-gray-600" />
				</button>
				<Dropdown>
					<DropdownItem on:click={restartConversation} class="flex items-center whitespace-nowrap">
						<RefreshOutline class="h-4 w-4 mr-2" />
						<span>{t('restart_conversation')}</span>
					</DropdownItem>
				</Dropdown>
			</div>
		</div>

		<!-- Changed from h-screen to a more flexible height -->
		<div class="flex flex-col w-full" style="height: 700px; padding: 0; overflow: hidden;">
			<!-- Main Content Area with Proper Scrolling -->
			<div class="flex-1 overflow-hidden flex flex-col w-full">
				<!-- Messages container with scrolling -->
				<div 
					bind:this={chatContainer}
					class="flex-1 overflow-y-auto p-4 space-y-4"
				>
					<!-- AI welcome message -->
					{#if !suggestions.length && !reply}
					<div class="flex justify-end">
						<div class="max-w-sm rounded-lg bg-blue-100 p-3 text-sm text-gray-700">
							Welcome! I can help you find information about insurance products, promotions, and support. What would you like to know?
						</div>
					</div>
					{/if}
		
					<!-- User query if sent -->
					{#if loading || suggestions.length || reply}
					<div class="flex justify-end">
						<div class="max-w-sm rounded-lg bg-blue-500 p-3 text-white text-sm">
							{userInput}
						</div>
					</div>
					{/if}
			
					<!-- Loading indicator -->
					{#if loading}
					<div class="flex justify-start">
						<div class="max-w-sm rounded-lg bg-white p-3 shadow">
							<div class="flex items-center">
								<Spinner size="4" />
								<span class="ml-2 text-sm text-gray-700">Searching for information...</span>
							</div>
						</div>
					</div>
					{/if}
		
					<!-- AI Reply -->
					{#if reply}
						<div class="flex justify-start">
							<div class="max-w-sm rounded-lg bg-yellow-50 border border-yellow-200 p-3 shadow">
								<div class="mb-2 text-sm">
									<div class="mb-1 font-semibold text-yellow-800">AI Response</div>
									<div class="text-gray-700">{reply}</div>
								</div>
								<div class="flex justify-end"> 
									<button 
										class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors" 
										on:click={() => copyToClipboard(reply)}
									>
										<FileCopyOutline class="h-4 w-4" />
									</button>
									<Tooltip>{t('copy')}</Tooltip>
								</div>
								
								<div class="my-2 border-t border-gray-200"></div>

								{#if followUpQuestions && followUpQuestions.length}
									<div class="mt-4">
										<div class="mb-1 text-sm font-semibold text-yellow-800">Follow-up Questions</div>
										<div class="flex flex-col space-y-2">
										{#each followUpQuestions as q}
											<div class="text-xs text-gray-600 hover:text-gray-800">{q}</div>
										{/each}
										</div>
									</div>
								{/if}
							</div>
						</div>
					{/if}

					{#if selectedReplyType === 'search'}
						<!-- Customer Support Results -->
						{#if categorizedSuggestions['customer_support']}
							{#each categorizedSuggestions['customer_support'] as suggestion, i}
								<div class="flex justify-start">
									<div class="max-w-sm rounded-lg bg-white border border-gray-200 p-3 shadow">
										<div class="mb-2 text-sm">
											<!-- <div class="mb-1 font-semibold text-blue-800">Support Info {i+1}</div> -->
											<div class="text-gray-700">
												<div class="mb-1"><strong>Question:</strong> {suggestion.metadata.question}</div>
												<div><strong>Answer:</strong> {suggestion.metadata.answer}</div>
											</div>
										</div>
										<div class="mt-2 flex justify-end">
											<button class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors">
												<BookOpenOutline class="h-4 w-4" />
											</button>
											<Tooltip>{suggestion.metadata.source.replace('temp_storage/', '')}</Tooltip>

											<button 
												class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors" 
												on:click={() => copyToClipboard(`Q: ${suggestion.metadata.question}\nA: ${suggestion.metadata.answer}`)}
											>
												<FileCopyOutline class="h-4 w-4" />
											</button>
											<Tooltip>{t('copy')}</Tooltip>
										</div>
									</div>
								</div>
							{/each}
						{/if}
		
						<!-- Promotion Results -->
						{#if categorizedSuggestions['promotion']}
							{#each categorizedSuggestions['promotion'] as suggestion, i}
								<div class="flex justify-start">
									<div class="max-w-sm rounded-lg bg-green-50 border border-green-200 p-3 shadow">
										<div class="mb-2 text-sm">
											<div class="mb-1 font-semibold text-green-800">Promotion {i+1}</div>
											<div class="text-gray-700">
												{@html suggestion.metadata.content}
											</div>
										</div>
										<div class="mt-2 flex justify-end">
											<button class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors">
												<BookOpenOutline class="h-4 w-4" />
											</button>
											<Tooltip>{suggestion.metadata.source.replace('temp_storage/', '')}</Tooltip>
											<button 
													class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors" 
													on:click={() => copyToClipboard(suggestion.metadata.content.replace(/<br>/g, '\n'))}
												>
												<FileCopyOutline class="h-4 w-4" />
											</button>
											<Tooltip>{t('copy')}</Tooltip>
										</div>
									</div>
								</div>
							{/each}
						{/if}
		
						<!-- Product Results -->
						{#if categorizedSuggestions['product']}
							{#each categorizedSuggestions['product'] as suggestion, i}
								<div class="flex justify-start">
									<div class="max-w-sm rounded-lg bg-purple-50 border border-purple-200 p-3 shadow">
										<div class="mb-2 text-sm">
											<div class="mb-1 font-semibold text-purple-800">Product {i+1}: {suggestion.metadata.name || ''}</div>
											<div class="grid grid-cols-1 gap-1 text-xs text-gray-700">
												{#each getSuggestionFields(suggestion.metadata) as [label, value]}
												<div><strong>{label}:</strong> {value}</div>
												{/each}
											</div>
											
										</div>
										<div class="mt-2 flex justify-end">
											<button class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors">
												<BookOpenOutline class="h-4 w-4" />
											</button>
											<Tooltip>{suggestion.metadata.source.replace('temp_storage/', '')}</Tooltip>
											<button 
												class="flex items-center text-xs text-gray-600 hover:text-gray-800 transition-colors" 
												on:click={() => copyToClipboard(`Product: ${suggestion.metadata.name || ''}\nPlan ID: ${suggestion.metadata.plan_id || ''}\nDescription: ${suggestion.metadata.description || ''}`)}
											>
												<FileCopyOutline class="h-4 w-4" />
											</button>
											<Tooltip>{t('copy')}</Tooltip>
										</div>
									</div>
								</div>
							{/each}
						{/if}
					{/if}
					
					<!-- Spacer for better scrolling -->
					<div class="h-4"></div>
				</div>
			</div>
		
			<!-- Bottom Section - Fixed Controls -->
			<div class="border-t w-full px-0">
				<!-- Input Area -->
				<div class="py-4">
					<div class="flex items-center space-x-2">
						<input
							type="text"
							bind:value={userInput}
							placeholder={t('ask_ai')}
							class="flex-1 rounded-lg border border-gray-300 px-4 py-1 focus:border-blue-500 focus:outline-none"
							on:keypress={(e) => e.key === 'Enter' && handleSend()}
						/>

						<button 
							class="rounded-lg bg-blue-500 p-2 text-white hover:bg-blue-600 disabled:bg-blue-300 transition-colors"
							on:click={handleSend}
							disabled={!userInput.trim() || loading}
						>
							<!-- <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
								<path stroke-linecap="round" stroke-linejoin="round" d="M12 19l9-7-9-7v14zm-2-7H3" />
							</svg> -->
							<PaperPlaneSolid class="h-5 w-5" />
						</button>
						<Tooltip>{t('send')}</Tooltip>
					</div>
				</div>

				<!-- Mode selector with reduced padding -->
				<div>
					<div class="flex flex-col space-y-2">
						<!-- Search Type -->
						<div class="flex items-center justify-between">
							<div class="text-sm font-medium text-gray-600">{t('search_type')}</div>
							<div class="flex space-x-1">
								<button 
									class={"px-2 py-1 text-xs font-medium rounded-lg transition-colors border " + (selectedReplyType === 'search' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
									on:click={() => handleTypeChange('search')}
								>
									<!-- Quick Search -->
									{t('quick_search')}
								</button>
								<Tooltip>Search for relevant information from uploaded documents</Tooltip>
								<button 
									class={"px-2 py-1 text-xs font-medium rounded-lg transition-colors border " + (selectedReplyType === 'smart_reply' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
									on:click={() => handleTypeChange('smart_reply')}
								>
									<!-- Smart Reply -->
									{t('smart_reply')}
								</button>
								<Tooltip>Generate AI-powered responses</Tooltip>
							</div>
						</div>
						
						<!-- Content Type -->
						<div class="flex items-center justify-between">
							<div class="text-sm font-medium text-gray-600">{t('content_type')}</div>
							<div class="flex space-x-1">
								{#if selectedReplyType === 'smart_reply'}
									<button 
										class={"p-1.5 rounded-lg transition-colors border " + (selectedDocType === 'all' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
										on:click={() => handleDocTypeChange('all')}
									>
										<FileInvoiceSolid class="h-4 w-4" />
									</button>
									<Tooltip>All</Tooltip>
								{/if}
								<button 
									class={"p-1.5 rounded-lg transition-colors border " + (selectedDocType === 'customer_support' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
									on:click={() => handleDocTypeChange('customer_support')}
								>
									<FolderOpenSolid class="h-4 w-4" />
								</button>
								<Tooltip>Customer Support</Tooltip>
								<button 
									class={"p-1.5 rounded-lg transition-colors border " + (selectedDocType === 'promotion' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
									on:click={() => handleDocTypeChange('promotion')}
								>
									<ThumbsUpSolid class="h-4 w-4" />
								</button>
								<Tooltip>Promotion</Tooltip>
								<button 
									class={"p-1.5 rounded-lg transition-colors border " + (selectedDocType === 'product' ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50')}
									on:click={() => handleDocTypeChange('product')}
								>
									<ShoppingBagSolid class="h-4 w-4" />
								</button>
								
								<Tooltip>Product</Tooltip>
							</div>
						</div>
					</div>

					<!-- Quick Actions with reduced padding -->
					<div class="mb-2 text-sm font-medium text-gray-600">{t('quick_action')}</div>
					<div class="flex flex-wrap gap-1">
						{#each quickActions as action}
							<button 
								class="rounded-lg border border-gray-300 bg-white px-2 py-1 text-xs text-gray-700 hover:bg-gray-50 transition-colors"
								on:click={() => handleQuickAction(action.label)}
							>
								{action.label}
							</button>
						{/each}
					</div>
				</div>
			</div>
		</div>
    </div>

	<!-- Template -->
	<div class="w-full p-6 mb-2 bg-white rounded-lg shadow-md"> 
		<div class="flex items-center justify-between">
			<div class="text-lg font-medium text-gray-700">{t('template_response')}</div>

			<Button size="sm" class="bg-black hover:bg-gray-700 text-white">
				+ {t('new_template')}
			</Button>  
		</div>
	</div>
</div>