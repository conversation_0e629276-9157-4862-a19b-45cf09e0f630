<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { get } from 'svelte/store';

	import type { Customer } from '$lib/types/customer';
	import { 
		Card, 
		Button, 
		Label, 
		Input, 
		Checkbox,
		AccordionItem,
		Hr,
		Dropdown,
		DropdownItem,
		Textarea,
		Indicator
	} from "flowbite-svelte";

	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline
	} from 'flowbite-svelte-icons';

	import { getColorClass, formatTimestamp } from '$lib/utils';
	import { getBackendUrl } from '$src/lib/config';

	import NoteEditModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteEditModal.svelte';
	import NoteDeleteModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteDeleteModal.svelte';
	import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
	import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';

	let editModal = false;
	let deleteModal = false;
	let editSummary;
	let deleteSummaryId;

	export let customer: Customer;
	export let platformId: number;
	export let access_token: string;

	function formatDate(dateString: string | null) {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	}

	// function formatTimestamp(timestamp) {
	// 	const date = new Date(timestamp);
	// 	const options = {
	// 		day: '2-digit',
	// 		month: 'short',
	// 		year: 'numeric',
	// 		hour: '2-digit',
	// 		minute: '2-digit',
	// 		hour12: false
	// 	};

	// 	const locale = $language === 'th' ? 'th-TH' : 'en-US';

	// 	return date.toLocaleString(locale, options).replace(',', '');
	// }
	
	// function getCustomerTypeColor(type: string) {
	// 	switch (type) {
	// 		case 'VIP': return 'bg-purple-100 text-purple-800';
	// 		case 'REGULAR': return 'bg-blue-100 text-blue-800';
	// 		case 'NEW': return 'bg-green-100 text-green-800';
	// 		default: return 'bg-gray-100 text-gray-800';
	// 	}
	// }
	
	import { services } from "$src/lib/api/features";
	// import { L } from 'vitest/dist/chunks/reporters.nr4dxCkA.js';

	let notes: any[] = [];
	let loadingNotes = false;
	let customerTags: any[] = [];
	let loadingTags = false;
	let ownersHistoryticket: any[] = [];
	let loadingHistory = false;


	$: if (customer && customer.customer_id && access_token) {
		loadCustomerNotes(customer.customer_id);
		loadCustomerTags();
		loadUserHistory();
	}

	async function loadUserHistory() {
		try {
			loadingHistory = true;
			
			// First request: Get platform info using customer_id and main_interface_id
			// const platformId =  customer.customer_id;
			const platformInfo = await services.customers.getPlatformInfo(customer.customer_id, platformId, access_token);
			
			// console.log('Platform info:', platformInfo);
			
			// Second request: Get ticket owners using the platform info id as backend_ticket_id
			const backendTicketId = platformInfo.id;
			const ticketOwnersResponse = await fetch(
				`${getBackendUrl()}/ticket/api/tickets/${backendTicketId}/owners/`,
				{
					headers: {
						'Authorization': `Bearer ${access_token}`,
						'Content-Type': 'application/json'
					}
				}
			);
			
			if (!ticketOwnersResponse.ok) {
				throw new Error('Failed to fetch ticket owners');
			}
			
			const ticketOwners = await ticketOwnersResponse.json();
			ownersHistoryticket = ticketOwners || [];
			// console.log('Ticket owners:', ticketOwners);
						
		} catch (error) {
			console.error('Error loading user history:', error);
			ownersHistoryticket = [];
		} finally {
			loadingHistory = false;
		}
	}

	async function loadCustomerTags() {
		try {
			loadingTags = true;
			const response_customer_tag = await services.customers.getAllTags(access_token);
			customerTags = response_customer_tag.customer_tags || [];
			// console.log(customerTags);
		} catch (error) {
			console.error('Error loading customer tags:', error);
			customerTags = [];
		} finally {
			loadingTags = false;
		}
	}

	async function loadCustomerNotes(customerId: number) {
		try {
			loadingNotes = true;
			const response_customer_notes = await services.customers.getCustomerNotes(customerId, access_token);
			notes = response_customer_notes.customer_notes || [];
			// console.log(notes)
		} catch (error) {
			console.error('Error loading customer notes:', error);
			notes = [];
		} finally {
			loadingNotes = false;
		}
	}
	
	function openEditModal(summary) {
		editModal = true;
		editSummary = { ...summary };
	}

	function openDeletelModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	let isNotesOpen = true;
	function toggleNotes() {
		isNotesOpen = !isNotesOpen;
	}

	export let loadingOwnerHistory = false;
	let isOwnerHistoryOpen = true;
	function toggleOwnerHistory() {
		isOwnerHistoryOpen = !isOwnerHistoryOpen;
	}

	$: ownerHistory = ownersHistoryticket?.owner_history || [];
	$: currentOwner = ownersHistoryticket?.current_owner;
</script>

<div class="w-full h-full p-4 space-y-6 overflow-y-auto">
	<!-- Customer Profile Card -->
	<div class="w-full p-4 mb-4 bg-white rounded-lg shadow-md">
		<!-- Customer Header -->
		<div class="text-center">
			<div class="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center text-2xl font-medium text-gray-600">
				{customer.name?.charAt(0) || 'C'}
				<!-- {customer.platform_identities[0].picture_url} -->
			</div>
			<h2 class="text-xl font-semibold">{customer.name || 'Unknown Customer'}</h2>
			<p class="text-sm text-gray-500">{customer.customer_id.toString()}</p>
		</div>
		
		<!-- Basic Information -->
		<div>
			<div class="flex items-center mb-2 justify-between">
				<div class="text-lg font-medium text-gray-700">{t('basic_information')}</div>
	
				<CustomerEdit {customer} />
			</div>

			<div class="space-y-3">
				<div>
					<label class="text-xs text-gray-500">{t('full_name')}</label>
					<p class="text-sm font-medium">
						{customer.first_name && customer.last_name ? `${customer.first_name} ${customer.last_name}`: t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('age')}</label>
					<p class="text-sm font-medium">{customer.age || t('not_provided')}</p>
				</div>

				<!-- <div>
					<label class="text-xs text-gray-500">{t('customer_type')}</label>
					<p class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getCustomerTypeColor(customer.customer_type)}">
						{customer.customer_type}
					</p>
				</div> -->
			<!-- </div> -->
		<!-- </div> -->

		<!-- Contact Information -->
		<!-- <div> -->
			<!-- <div class="space-y-3"> -->
				<div>
					<label class="text-xs text-gray-500">{t('phone_number')}</label>
					<p class="text-sm font-medium">{customer.phone || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('email')}</label>
					<p class="text-sm font-medium">{customer.email || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('address')}</label>
					<p class="text-sm font-medium">
						{customer.address.address_line1 || t('not_provided')}
						{#if customer.address.address_line2}
							<br>{customer.address.address_line2}
						{/if}
						{#if customer.address.district || customer.address.province}
							<br>{[customer.address.district, customer.address.province].filter(Boolean).join(', ')}
						{/if}
					</p>
				</div>

				<!-- <div>
					<label class="text-xs text-gray-500">{t('country')}</label>
					<p class="text-sm font-medium">{customer.country || 'Thailand'}</p>
				</div> -->
			<!-- </div> -->
		<!-- </div> -->

		<!-- Organization -->
		<!-- <div> -->
			<!-- <div class="space-y-3"> -->
				<div>
					<label class="text-xs text-gray-500">{t('company')}</label>
					<p class="text-sm font-medium">{customer.company_name || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('contact_channel')}</label>
					<p class="text-sm font-medium">{customer.main_interface_id?.name || 'LINE'}</p>
				</div>
			</div>
		</div>

		
		<!-- Platform Identities -->
		<!-- <div>
			<h3 class="text-sm font-medium text-gray-700 mb-3">Connected Platforms</h3>
			<div class="space-y-2">
				{#if customer.platforms && customer.platforms.length > 0}
					{#each customer.platforms as platform}
						<div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
							<div class="flex items-center space-x-2">
								<span class="text-lg">{platform.platform === 'LINE' ? '💚' : '💬'}</span>
								<span class="text-sm font-medium">{platform.platform}</span>
							</div>
							{#if platform.verified}
								<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
							{/if}
						</div>
					{/each}
				{:else}
					<p class="text-sm text-gray-500">No connected platforms</p>
				{/if}
			</div>
		</div>
		 -->
		<!-- Tags -->
		<!-- {#if customer.tags && customer.tags.length > 0}
			<div>
				<h3 class="text-sm font-medium text-gray-700 mb-3">Tags</h3>
				<div class="flex flex-wrap gap-2">
					{#each customer.tags as tag}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
							{tag}
						</span>
					{/each}
				</div>
			</div>
		{/if} -->
	</div>

	<!-- Customer Tags -->
	<div class="w-full p-4 mb-4 bg-white rounded-lg shadow-md"> 
		<div class="flex items-center mb-2 justify-between">
			<div class="text-lg font-medium text-gray-700">{t('customer_tags')}</div>

			<CustomerTag customer={customer} customer_tags={customerTags}/>
		</div>
	
		<div class="flex flex-wrap gap-3">
			{#if customer.tags && customer.tags.length > 0}
				{#each customer.tags as tag}
					<!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
					<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
						<!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
						<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
						{tag.name}
					</span>
				{/each}
			{:else}
				<span class="text-sm text-gray-500">{t('no_tags')}</span>
			{/if}
		</div>		
	</div>
	
	<!-- Staff History -->
	<div class="w-full p-4 mb-4 bg-white rounded-lg shadow-md">
		<div class="w-full">
			<div 
				class="flex items-center justify-between rounded-lg cursor-pointer transition-colors"
				on:click={toggleOwnerHistory}
				on:keydown={(e) => e.key === 'Enter' && toggleOwnerHistory()}
				role="button"
				tabindex="0"
			>
				<div class="text-lg font-medium text-gray-700">{t('staff_history')}</div>
				{#if ownerHistory.length > 0}
					<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
						<span>{ownerHistory.length} {t('employee')}</span>
						<AngleDownOutline 
							class="w-4 h-4 ml-1 transform transition-transform duration-200 {isOwnerHistoryOpen ? 'rotate-180' : ''}"
						/>
					</div>
				{/if}
			</div>
	
			{#if isOwnerHistoryOpen}
				<div class="mt-4 transition-all duration-300 ease-in-out">
					{#if loadingOwnerHistory}
						<div class="flex items-center justify-center py-8">
							<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
							<span class="ml-2 text-sm text-gray-500">{t('loading_owner_history')}</span>
						</div>
					{:else if ownerHistory.length > 0}
						<div class="space-y-4">
							<!-- Owner History List -->
							{#each ownerHistory as historyItem, index}
								{@const owner = historyItem.owner || historyItem.created_by_user}
								{@const isCurrentOwner = index === 0 || historyItem.is_current}
								
								<div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3">
									<div class="flex items-center justify-between">
										<div class="flex items-center space-x-3">
											<!-- Avatar -->
											<!-- <div class="w-12 h-12 rounded-full flex items-center justify-center text-white font-medium {getAvatarColor(owner?.name || owner?.username)}">
												{getInitials(owner?.name || owner?.username)}
											</div> -->
											
											<!-- Owner Info -->
											<div>
												<div class="font-medium text-gray-900">
													{owner?.name || owner?.username || t('unknown')}
												</div>
												<div class="text-sm text-gray-500">
													{t('role')}: {owner?.roles}
												</div>
												<div class="text-xs text-gray-400">
													{t('assigned_on')}: {formatTimestamp(historyItem.created_on)}
												</div>
												{#if historyItem.note}
													<div class="text-xs text-gray-600 mt-1">
														{t('note')}: {historyItem.note}
													</div>
												{/if}
											</div>
										</div>
										
										<!-- Current Badge -->
										{#if isCurrentOwner}
											<span class="px-3 py-1 text-sm font-medium text-green-700 bg-green-100 rounded-full">
												{t('current')}
											</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<div class="text-center py-8">
							<div class="text-gray-400 mb-2">
								<svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
								</svg>
							</div>
							<p class="text-gray-500">{t('no_owner_history_available')}</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
	
	<!-- Notes -->
	<div class="w-full p-4 mb-4 bg-white rounded-lg shadow-md"> 
		<div class="w-full">
			<div 
				class="flex items-center justify-between rounded-lg cursor-pointer transition-colors"
				on:click={toggleNotes}
			>
				<div class="text-lg font-medium text-gray-700">{t('notes')}</div>
				{#if notes.length > 0}
					<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
						<span>{notes.length} {t('notes')}</span>
						<svg 
							class="w-4 h-4 ml-1 transform transition-transform duration-200 {isNotesOpen ? 'rotate-180' : ''}" 
							fill="none" 
							stroke="currentColor" 
							viewBox="0 0 24 24"
						>
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
						</svg>
					</div>
				{/if}
			</div>
		
			{#if isNotesOpen}
				<div class="mt-4 transition-all duration-300 ease-in-out">
					{#if loadingNotes}
						<div class="flex items-center justify-center py-8">
							<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
							<span class="ml-2 text-sm text-gray-500">{t('loading_notes')}</span>
						</div>
					{:else if notes.length > 0}
						<div class="space-y-4">

							<form
								method="POST"
								enctype="multipart/form-data"
								action="?/upload_note"
								use:enhance={() => {
									return async ({ update, result }) => {
										console.log(result);
										if (result.type === 'success') {
											await update();
											// Reload notes after successful creation
											await loadCustomerNotes(customer.customer_id);
										}
									};
								}}
								class="mb-4 p-4 bg-gray-50 rounded-lg"
							>
								<input type="hidden" name="customer_id" value={customer.customer_id} />
								
								<div class="mb-3">
									<!-- <Label for="note_content">Add New Note</Label> -->
									<Textarea
										id="note_content"
										name="content"
										placeholder={t('new_note')}
										rows="3"
										required
										class="mt-1"
									/>
								</div>
								
								<Button color="dark" type="submit" class="w-full">
									<PlusOutline class="w-4 h-4 mr-2" />
									{t('add_note')}
								</Button>
							</form>
							
							{#each notes as note}
								<div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3">
									<div class="flex items-center space-x-2 mb-2">
										<div>
											<span class="font-sm text-gray-900">{note.created_by_name}</span>
											<span class="text-sm text-gray-500">
												{formatTimestamp(note.created_on)}
												<!-- {#if note.updated_on !== note.created_on}
													({t('edited')})
												{/if} -->
											</span>
										</div>

										<div>
											<Button
												color="light"
												class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
											>
												<AngleDownOutline class="h-4 w-4" />
											</Button>
			
											<Dropdown>
												<DropdownItem class="flex items-center space-x-2" on:click={openEditModal(note)}>
													{t('edit')} <PenOutline class="h-4 w-4" />
												</DropdownItem>
												<DropdownItem
													class="flex items-center space-x-2"
													on:click={openDeletelModal(note.id)}
												>
													{t('delete')} <TrashBinOutline class="h-4 w-4" />
												</DropdownItem>
											</Dropdown>
										</div>
									</div>

									<div class="text-gray-700 whitespace-pre-wrap leading-relaxed">
										{note.content}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<div class="text-center py-8">
							<div class="text-gray-400 mb-2">
								<svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
								</svg>
							</div>
							<p class="text-gray-500">{t('no_notes_available')}</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
</div>

<NoteEditModal 
	bind:editModal 
	editNote={editSummary} 
	customerId={customer.customer_id} 
	onSuccess={() => loadCustomerNotes(customer.customer_id)}
/>

<NoteDeleteModal 
	bind:deleteModal 
	deleteNoteId={deleteSummaryId} 
	customerId={customer.customer_id} 
	onSuccess={() => loadCustomerNotes(customer.customer_id)}
/>