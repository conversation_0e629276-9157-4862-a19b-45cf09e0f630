<script lang="ts">

    // import { PUBLIC_BACKEND_URL } from '$env/static/public';
	import { t } from '$src/lib/stores/i18n';
    import { page } from '$app/stores';

    export let customer: CustomerInterface;
    // export let customer_tags: any;
    import { Indicator } from "flowbite-svelte";
    import { getColorClass } from '$lib/utils';


    $: role = $page.data.role;
    $: isAgent = role === 'Agent';
    console.log({role});
    console.log(role);

    function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 6) + phone.slice(len - 3);
        }
        return phone;
    }


    const hasValidAddress = (address) => {
        if (!address) return false;
        
        const addressFields = [
            'address_line1',
            'address_line2', 
            'city',
            'state_province_region',
            'zip_code',
            'country'
        ];
        
        return addressFields.some(field => 
            address[field] && address[field].toString().trim() !== ''
        );
    };

    // Helper function to format address for display
    const formatAddress = (address) => {
    if (!address) return '';
    
    const parts = [];
    
    if (address.address_line1) parts.push(address.address_line1);
    if (address.address_line2) parts.push(address.address_line2);
    if (address.city) parts.push(address.city);
    if (address.state_province_region) parts.push(address.state_province_region);
    if (address.zip_code) parts.push(address.zip_code);
    if (address.country) parts.push(address.country);
    
    return parts.join(', ');
    };
</script>

<div class="w-full space-y-5">
    <!-- Customer Profile -->
    <div class="space-y-4 p-6 border bg-white rounded-lg shadow-md"> 
        <div class="grid">
            <!-- Profile image -->
            <div class="flex justify-center mb-6">
                <div class="h-32 w-32 rounded-full border-2 border-gray-400 shadow-md overflow-hidden">
                    <img
                        src={customer.picture_url
                            ? customer.picture_url
                            : '/images/person-logo.png'}
                        alt="Profile"
                        class="w-full h-full object-cover"
                    />
                </div>
            </div>

            <!-- Left side: Customer Profile and Notes -->
            <!-- Customer Profile -->
            <h3 class="text-sm text-gray-500 font-medium mb-3">{t('customer_details')}</h3>
            
            <!-- No. -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11h10M7 15h10M9 7l-1 10m8-10l-1 10" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('no')}</span>
                    </div>
                </div>
                <div class="ml-6 text-sm text-blue-500">{customer.customer_id}</div>
            </div>

            <!-- Name -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 14c1.657 0 3 1.343 3 3v1H5v-1c0-1.657 1.343-3 3-3h8zM12 12a4 4 0 100-8 4 4 0 000 8z" />
                          </svg>
                        <span class="text-sm text-gray-600">{t('name')}</span>
                    </div>
                </div>
                <div class="ml-6 text-sm text-blue-500">{customer.name ? customer.name : customer.line_user ? customer.line_user.display_name: '-'}</div>
            </div>

            <!-- Age -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('age')}</span>
                    </div>
                </div>
                <div class="ml-6 text-sm text-blue-500">{customer.age ? customer.age : ''}</div>
            </div>

            <!-- Social Platform -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a4 4 0 00-5-3.87M9 20H4v-2a4 4 0 015-3.87m6 1.87a4 4 0 100-8 4 4 0 000 8zm-6 0a4 4 0 100-8 4 4 0 000 8z" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('social_platform')}</span>
                    </div>
                </div>
                <div class="ml-6 text-sm text-blue-500">{customer.main_interface.name ? customer.main_interface.name : ''}</div>
            </div>


            <!-- Phone Number -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('phone_number')}</span>
                    </div>
                </div>
                <div class="ml-6 text-sm text-blue-500">{maskPhoneNumber(customer.phone)}</div>
            </div>
                    
            <!-- Email -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('email')}</span>
                    </div>
                    <!-- <button class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                    </button> -->
                </div>
                <div class="ml-6 text-sm">
                    <a href="mailto:{customer.email}" class="text-blue-500">{customer.email || ''}</a>
                    <!-- {#if customer.secondary_email}
                    <br>
                    <a href="mailto:{customer.secondary_email}" class="text-blue-500">{customer.secondary_email}</a>
                    {/if} -->
                </div>
            </div>

            <!-- Tag -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <div class="flex items-center">
                        <!-- Tag Icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        <span class="text-sm text-gray-600">{t('tag')}</span>
                    </div>
                </div>

                <!-- <div class="ml-6 text-sm text-blue-500">{customer.tags.map((tag) => tag.name) || ''}</div> -->

                {#each customer.tags as tag}
					<!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
					<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
						<!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
						<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
						{tag.name}
					</span>
				{/each}
            </div>

            <!-- Address -->
            <div class="mb-4">
                <div class="flex items-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-sm text-gray-600">{t('address')}</span>
                </div>
                <div class="ml-6 text-sm text-blue-500">
                <!-- {customer.address || t('no_address')}  -->
                    {hasValidAddress(customer.address) 
                        ? formatAddress(customer.address)
                        : t('no_address')
                    }
                </div>
            </div>
                
            <!-- Language Spoken -->
            <!-- <div class="mb-4">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                            </svg>
                            <span class="text-sm text-gray-600">Language Spoken</span>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>
                    <div class="ml-6 flex flex-wrap gap-1">
                        <span class="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs">
                            English
                            <button class="ml-1 text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </span>
                        <span class="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs">
                            Italian
                            <button class="ml-1 text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </span>
                    </div>
                </div> -->

                <!-- Organization -->
                <!-- <div class="mb-4">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <span class="text-sm text-gray-600">Organization</span>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>
                    <div class="ml-6 text-sm flex items-center">
                        <span class="inline-block h-3 w-3 bg-blue-500 mr-2"></span>
                        Microsoft
                    </div>
                </div> -->
                
                <!-- Description -->
                <!-- <div>
                    <div class="flex items-center mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span class="text-sm text-gray-600">Description</span>
                    </div>
                    <div class="ml-6 text-sm text-gray-400">Add Description</div>
                </div> -->
            <!-- </div> -->
            <!-- </div> -->
        </div>
    </div>
</div>