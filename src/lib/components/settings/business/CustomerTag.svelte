<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import {
        Accordion,
        AccordionItem,
        Indicator
    } from 'flowbite-svelte';

    import { PlusOutline, MinusOutline, TrashBinSolid, EditOutline, CheckOutline } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
    import { invalidateAll } from '$app/navigation';
    import { onMount, tick } from 'svelte';

    interface Tag { id: number; name: string; color?: string; }
    interface ColorOption { name: string; class: string; }

    export let customerTagNames: Tag[] = [];
    import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

    let isAddingTag = false;
    let tagFormErrors: string | null = null;
    let tagToDelete: number | null = null;
    let tagToEdit: number | null = null;
    let isSubmittingTag = false;

    // Shared state for color picker
    let selectedColor = colorOptions[0]?.name || 'gray';
    let colorPickerOpen = false;
    let activePickerId: string | null = null;
    
    // State for new tag
    let newTagColor = colorOptions[0]?.name || 'gray';

    onMount(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
          colorPickerOpen = false;
          activePickerId = null;
        }
      };
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    });

    async function toggleColorPicker(id: string) {
      if (activePickerId === id && colorPickerOpen) {
        colorPickerOpen = false;
        activePickerId = null;
      } else {
        activePickerId = id;
        await tick();
        colorPickerOpen = true;
      }
    }

    function chooseColor(name: string) {
      if (tagToEdit !== null) {
        selectedColor = name;
      } else {
        newTagColor = name;
      }
      colorPickerOpen = false;
      activePickerId = null;
    }

    function handleTagSubmit() {
        isSubmittingTag = true;
        tagFormErrors = null;

        return async ({ result, update }) => {
            isSubmittingTag = false;

            if (result.type === 'success') {
                // Reset form and hide the form
                const form = document.querySelector('form') as HTMLFormElement;
                form?.reset();
                isAddingTag = false;
                tagToEdit = null;
                
                // Invalidate all data to force a full reload
                await invalidateAll();
            } else if (result.type === 'failure') {
                tagFormErrors = result.data?.error || 'An unexpected error occurred';
            }
        };
    }

    function confirmTagDelete(tagId: number) {
        tagToDelete = tagId;
    }

    function cancelTagDelete() {
        tagToDelete = null;
    }

    function startTagEdit(tag) {
        tagToEdit = tag.id;
        selectedColor = tag.color || colorOptions[0]?.name || 'gray';
        activePickerId = null;
        tagFormErrors = null;
    }

    function cancelTagEdit() {
        tagToEdit = null;
        tagFormErrors = null;
        colorPickerOpen = false;
        activePickerId = null;
    }
</script>


<AccordionItem open>
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-xl font-medium text-gray-700">{t('tags_title')}</h2>
        <p class="text-sm text-gray-500">{t('tags_description')}</p>
    </span>
    <div class="space-y-3">

        {#if customerTagNames.length > 0}
            <ul class="space-y-2">
                {#each customerTagNames as tag (tag.id)}
                    <li class="px-4 py-2 rounded-lg flex justify-between items-center">
                        {#if tagToEdit === tag.id}
                            <div class="relative flex items-center flex-1 gap-3">
                                <button
                                    type="button"
                                    class="flex items-center"
                                    on:click|stopPropagation={() => toggleColorPicker(`tag-${tag.id}`)}
                                    aria-label="Select color"
                                >
                                    <!-- <Indicator size="lg" color={selectedColor} class="mr-2" /> -->
                                    <Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
                                </button>
                                
                                {#if activePickerId === `tag-${tag.id}` && colorPickerOpen}
                                    <div class="absolute bottom-full left-0 mb-2 z-20 p-3 bg-white rounded-lg shadow-lg color-picker-area" style="min-width: 170px;">
                                        <div class="grid grid-cols-6 gap-3">
                                            {#each colorOptions as opt}
                                                <button
                                                    type="button"
                                                    class={`w-6 h-6 rounded-full cursor-pointer ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
                                                    on:click|stopPropagation={() => chooseColor(opt.name)}
                                                    aria-label={`Select ${opt.name} color`}
                                                ></button>
                                            {/each}
                                        </div>
                                    </div>
                                {/if}
                                
                                <form 
                                    method="POST" 
                                    action="?/update_customer_tag"
                                    use:enhance={handleTagSubmit}
                                    class="flex flex-1 items-center gap-2"
                                >
                                    <input 
                                        type="hidden" 
                                        name="tag_id" 
                                        value={tag.id} 
                                    />
                                    <input type="hidden" name="color" value={selectedColor} />
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        required
                                        value={tag.name}
                                        class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
                                        placeholder={t('enter_tag_name')}
                                    />
                                    
                                    <button type="submit" disabled={isSubmittingTag} class="text-green-600 hover:text-green-700 text-sm hover:underline">
                                        {isSubmittingTag ? t('updating') : t('update')}
                                    </button>
                                    <button type="button" on:click={cancelTagEdit} class="text-gray-500 hover:text-gray-700 text-sm hover:underline">
                                        {t('cancel')}
                                    </button>
                                </form>
                            </div>
                        {:else}
                            <div class="flex items-center">
                                <button type="button" class="flex items-center">
                                    <!-- <Indicator size="lg" color={tag.color ?? 'gray'} class="mr-2" /> -->
                                    <Indicator size="lg" class={`${getColorClass(tag.color)} mr-2`} />
                                    <span class="font-xl text-md text-gray-900">{tag.name}</span>
                                </button>
                            </div>

                            <div class="flex items-center space-x-2">
                                {#if tagToDelete !== tag.id}
                                    <button 
                                        on:click={() => startTagEdit(tag)}
                                        class="text-gray-400 hover:text-gray-800"
                                    >
                                        <EditOutline class="w-5 h-5" />
                                    </button>
                            
                                    <button 
                                        on:click={() => confirmTagDelete(tag.id)}
                                        class="text-red-500 hover:text-red-700"
                                    >
                                        <TrashBinSolid class="w-5 h-5" />
                                    </button>
                                {:else}
                                    <div class="flex space-x-2">
                                        <form 
                                            method="POST" 
                                            action="?/delete_customer_tag"
                                            use:enhance={handleTagSubmit}
                                            class="flex items-center"
                                        >
                                            <input 
                                                type="hidden" 
                                                name="tag_id" 
                                                value={tag.id} 
                                            />
                                            <button 
                                                type="submit" 
                                                disabled={isSubmittingTag}
                                                class="text-red-600 hover:text-red-800 text-sm hover:underline"
                                            >
                                                {t('delete')}
                                            </button>
                                        </form>
                                        <button 
                                            on:click={cancelTagDelete} 
                                            class="text-gray-500 hover:text-gray-800 text-sm hover:underline"
                                        >
                                            {t('cancel')}
                                        </button>
                                    </div>
                                {/if}
                            </div>
                            
                        {/if}
                    </li>
                    <hr class="my-6 border-t border-gray-300" />
                {/each}
            </ul>
        {:else if !isAddingTag}
            <p class="text-gray-500 italic text-center">{t('no_tags')}</p>
        {/if}

        <div class="w-full">
            {#if isAddingTag}
                <div class="relative flex items-center gap-3">
                    <button
                        type="button"
                        class="flex items-center pl-4"
                        on:click|stopPropagation={() => toggleColorPicker('new-tag')}
                        aria-label="Select color"
                    >
                        <!-- <Indicator size="lg" color={newTagColor} class="mr-2" /> -->
                        <Indicator size="lg" class={`mr-1 ${getColorClass(newTagColor)}`} />
                    </button>
                    {#if activePickerId === 'new-tag' && colorPickerOpen}
                        <div class="absolute bottom-full left-0 mb-2 z-20 p-3 bg-white rounded-lg shadow-lg color-picker-area" style="min-width: 170px;">
                            <div class="grid grid-cols-6 gap-3">
                                {#each colorOptions as opt}
                                    <button
                                        type="button"
                                        class={`w-6 h-6 rounded-full cursor-pointer ${opt.class} border ${newTagColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
                                        on:click|stopPropagation={() => chooseColor(opt.name)}
                                        aria-label={`Select ${opt.name} color`}
                                    ></button>
                                {/each}
                            </div>
                        </div>
                    {/if}
                    <form 
                        method="POST" 
                        action="?/create_new_customer_tag_action"
                        use:enhance={handleTagSubmit}
                        class="flex flex-1 items-center gap-2"
                    >
                        <input type="hidden" name="color" value={newTagColor} />
                        <input
                            type="text"
                            id="new_tag_name"
                            name="name"
                            required
                            class="flex-1 px-4 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder={t('enter_tag_name')}
                        />
                        <button 
                            type="button"
                            on:click={() => {
                                isAddingTag = false;
                                tagFormErrors = null;
                            }}
                            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-xl shadow-sm hover:bg-gray-100 transition"
                        >
                            {t('cancel')}
                        </button>
                        
                        <button 
                            type="submit" 
                            disabled={isSubmittingTag}
                            class="px-4 py-2 bg-green-600 text-white rounded-lg shadow-sm hover:bg-green-700 transition disabled:opacity-50"
                        >
                            {isSubmittingTag ? t('creating') : t('confirm')}
                        </button>
                    </form>
                </div>
          
                {#if tagFormErrors}
                    <div class="text-red-500 text-sm mt-1">{tagFormErrors}</div>
                {/if}
            {:else}
                <div class="flex justify-start">
                    <button 
                        on:click={() => {
                            isAddingTag = true;
                            tagFormErrors = null;
                        }}
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg border hover:bg-gray-100"
                    >
                        <PlusOutline class="w-5 h-5 mr-1" />
                        <span>{t('add_tag')}</span>
                    </button>
                </div>
            {/if}
        </div>
    </div>
</AccordionItem>