<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import { Toast } from 'flowbite-svelte';
    import { fly } from 'svelte/transition';

    import {
        CheckCircleSolid,
        ChevronDownOutline,
        PlusOutline
    } from 'flowbite-svelte-icons';

    import { Input, AccordionItem, Accordion, Dropzone } from 'flowbite-svelte';
    import { Button } from 'flowbite-svelte';

    export let connectionSettings;
    import ConnectionFacebook from './ConnectionFacebook.svelte';
    import ConnectionWhatapps from './ConnectionWhatapps.svelte';
    import ConnectionLine from './ConnectionLINE.svelte';

    // Import the new LINE Business connector modal
    import ConnectorLineBusiness from '../../connector/ConnectorLineBusiness.svelte';

    let toastStatus = false;
    let toastMessage = 'Changes saved successfully!';
    let counter = 0;

    // Add modal state
    let showLineModal = false;

    // Countdown timer for toast
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }

    // Watch toastStatus and start countdown when it becomes true
    // Not sure when this toast is used
    $: if (toastStatus) {
        counter = 3;
        timeout();
    }

    // Function to handle LINE connection button click
    function handleLineConnect() {
        showLineModal = true;
    }
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    {#if toastStatus}
        <Toast
            color="green"
            transition={fly}
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-1/2 top-20 -translate-x-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}

    <!-- <Accordion flush>
        <ConnectionLine {connectionSettings}/>
        <ConnectionWhatapps />
        <ConnectionFacebook />
    </Accordion> -->

    <!-- Chat Integrations Section -->
    <div class="space-y-6 border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">{t('chat_integrations')}</h2>
                <p class="text-sm text-gray-600 mt-1">{t('chat_integrations_description')}</p>
            </div>
        </div>

        <!-- Modify Here -->
    
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">{t('discover')}</h3>
    
            <!-- Line -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-1">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <img src="/images/platform-line.png" alt="Line" class="w-6 h-6" />
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('line_official_account')}</div>
                        <div class="text-sm text-gray-600">{t('connect_line_description')}</div>
                    </div>
                </div>
                <!-- <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    + {t('connect')}
                </button> -->
                <button 
                    class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors"
                    on:click={handleLineConnect}
                >
                    + {t('connect')}
                </button>
            </div>
    
            <!-- Facebook / Instagram -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="relative w-10 h-8">
                        <div class="absolute left-0 top-0 w-8 h-8 flex items-center justify-center z-10">
                            <img src="/images/platform-facebook.png" alt="Facebook" class="w-6 h-6" />
                        </div>
                        <div class="absolute left-4 top-0 w-8 h-8 flex items-center justify-center z-20">
                            <img src="/images/platform-instagram.png" alt="Instagram" class="w-6 h-6" />
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('facebook_instagram')}</div>
                        <div class="text-sm text-gray-600">{t('connect_line_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div>
    
            <!-- WhatsApp Business -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-whatsapp.png" alt="WhatsApp" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('whatsapp_business')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div>
    
            <!-- Shopee -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-shopee.png" alt="Shopee" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('shopee')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
    
            <!-- Lazada -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-lazada.png" alt="Lazada" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('lazada')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
    
            <!-- TikTok Shop -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-tiktok.png" alt="TikTok" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('tiktok_shop')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
        </div>
    </div>
    
</div>

<!-- LINE Business Connection Modal -->
<ConnectorLineBusiness bind:showModal={showLineModal} {connectionSettings} />