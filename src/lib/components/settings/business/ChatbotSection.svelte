<!-- ChatbotSection.svelte -->
<script lang="ts">
    import { onDestroy } from 'svelte';
    import { enhance } from '$app/forms';
    import { Toast } from 'flowbite-svelte';
    import { fly } from 'svelte/transition';
    import {
        CloseCircleSolid,
        CheckCircleSolid
    } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n';

    export let botSettings;

    // Options
    const genderOptions = ['Male', 'Female'];
    const conversationStyles = ['Formal', 'Casual', 'Professional', 'Friendly'];
    const rolesOptions = ['Customer Support'];
    const conversationTypes = [
        'Chat', 
        // 'Calling'
    ];

    // Track changes
    let originalValues = {};
    let changedFields = new Set();
    let hasUnsavedChanges = false;

    // Form reference
    let settingsForm;

    // Toast status and counter
    let showSaveSuccess = false;
    let toastStatus = false;
    let toastMessage = 'Changes saved successfully!';
    let counter = 0;

    // Initialize original values when component loads
    $: {
        if ($botSettings && Object.keys($botSettings).length > 0 && Object.keys(originalValues).length === 0) {
            originalValues = { ...$botSettings };
        }
    }

    // Track field changes
    function trackChange(field) {
        if ($botSettings[field] !== originalValues[field]) {
            changedFields.add(field);
        } else {
            changedFields.delete(field);
        }
        hasUnsavedChanges = changedFields.size > 0;
    }

    // Enhance options for form submission
    const enhanceOptions = {
        pending: () => {},
        error: () => {
            // Handle errors
            showSaveSuccess = false;
            toastStatus = false;
        },
        success: () => {
            originalValues = { ...$botSettings };
            changedFields.clear();
            hasUnsavedChanges = false;
            toastMessage = 'Changes saved successfully!';
            toastStatus = true;
            counter = 2;
            timeout();
        }
    };

    // Handle form submission
    function handleSubmit() {
        // Any additional handling before form submission
    }

    // Save bot settings
    function saveSettings() {
        const settings = [
            // Chatbot settings
            { key: "CHATBOT_MASCOT_THAI_NAME", value: $botSettings.thaiName },
            { key: "CHATBOT_MASCOT_ENGLISH_NAME", value: $botSettings.englishName },
            { key: "CHATBOT_ROLE", value: $botSettings.role },
            { key: "CHATBOT_GENDER", value: $botSettings.gender },
            { key: "CHATBOT_CONVERSATION_STYLE", value: $botSettings.conversationStyle },
            { key: "CHATBOT_CONVERSATION_TYPE", value: $botSettings.conversationType },
        ];

        const settingsInput = settingsForm.querySelector('input[name="settings"]');
        settingsInput.value = JSON.stringify(settings);

        toastStatus = true;
        counter = 2;
        timeout();

        settingsForm.requestSubmit();
    }

    function saveChanges() {
        saveSettings();
        showSaveSuccess = true;
        setTimeout(() => {
            showSaveSuccess = false;
            // Reset unsaved changes message after saving
            hasUnsavedChanges = false;
        }, 3000);
    }

    // Countdown timer for toast
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }

    // Handle page navigation - reset to original values
    onDestroy(() => {
        if (hasUnsavedChanges) {
            $botSettings = { ...originalValues };
        }
    });
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <!-- Success toast notification using Flowbite Svelte Toast -->
    {#if toastStatus}
        <Toast
            color="green"
            transition={fly}
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}

    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-medium text-gray-700">{t('chatbot_title')}</h2>
            <p class="text-sm text-gray-500">{t('chatbot_description')}</p>
        </div>

        <!-- Save changes button -->
        <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm
            {hasUnsavedChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
            on:click={saveChanges}
            disabled={!hasUnsavedChanges}
        >
            {#if hasUnsavedChanges}
                Save Changes
            {:else}
                Save
            {/if}
        </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="botThaiName" class="block text-sm font-medium text-gray-700">
                {t('chatbot_thai_name')}
                {#if changedFields.has('thaiName')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <input
                type="text"
                id="botThaiName"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('thaiName') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.thaiName}
                on:input={() => trackChange('thaiName')}
            />
        </div>

        <div>
            <label for="botEnglishName" class="block text-sm font-medium text-gray-700">
                {t('chatbot_english_name')}
                {#if changedFields.has('englishName')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <input
                type="text"
                id="botEnglishName"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('englishName') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.englishName}
                on:input={() => trackChange('englishName')}
            />
        </div>

        <div>
            <label for="role" class="block text-sm font-medium text-gray-700">
                {t('chatbot_role')}
                {#if changedFields.has('role')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <select
                id="role"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('role') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.role}
                on:change={() => trackChange('role')}
            >
                <option value="">{t('select_role')}</option>
                {#each rolesOptions as role}
                    <option value={role}>{role}</option>
                {/each}
            </select>
        </div>

        <div>
            <label for="gender" class="block text-sm font-medium text-gray-700">
                {t('chatbot_gender')}
                {#if changedFields.has('gender')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <select
                id="gender"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('gender') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.gender}
                on:change={() => trackChange('gender')}
            >
                <option value="">{t('select_gender')}</option>
                {#each genderOptions as gender}
                    <option value={gender}>{gender}</option>
                {/each}
            </select>
        </div>

        <div>
            <label for="conversationStyle" class="block text-sm font-medium text-gray-700">
                {t('chatbot_style')}
                {#if changedFields.has('conversationStyle')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <select
                id="conversationStyle"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('conversationStyle') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.conversationStyle}
                on:change={() => trackChange('conversationStyle')}
            >
                <option value="">{t('select_style')}</option>
                {#each conversationStyles as type}
                    <option value={type}>{type}</option>
                {/each}
            </select>
        </div>

        <div>
            <label for="conversationType" class="block text-sm font-medium text-gray-700">
                {t('chatbot_type')}
                {#if changedFields.has('conversationType')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <select
                id="conversationType"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                {changedFields.has('conversationType') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$botSettings.conversationType}
                on:change={() => trackChange('conversationType')}
            >
                <option value="">{t('select_type')}</option>
                {#each conversationTypes as type}
                    <option value={type}>{type}</option>
                {/each}
            </select>
        </div>
    </div>

    <!-- Moved the warning notification to the bottom -->
    {#if hasUnsavedChanges}
        <div class="bg-amber-50 border-l-4 border-amber-400 p-4 mt-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-amber-700">{t('unsaved_changes')}</p>
                </div>
            </div>
        </div>
    {/if}

    <form
        bind:this={settingsForm}
        action="?/update_system_setting"
        method="POST"
        use:enhance={() => enhanceOptions}
        on:submit={handleSubmit}
        class="hidden"
    >
        <input type="hidden" name="settings" value="">
    </form>     
</div>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md mt-6"> 
    <div>
        <h2 class="text-xl font-medium text-gray-700">{t('chatbot_workflow')}</h2>
        <p class="text-sm text-gray-500">{t('chatbot_workflow_description')}</p>
    </div>

    <p class="text-sm text-gray-500">{t('underprogress')}</p>
</div>