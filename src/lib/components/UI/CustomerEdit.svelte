<script lang="ts">
    import { t, language } from '$src/lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Button,
        Modal,
        Label,
        Input,
        Alert,
        Select,
    } from 'flowbite-svelte';
    import countries from 'i18n-iso-countries';
    import en from 'i18n-iso-countries/langs/en.json';
    import th from 'i18n-iso-countries/langs/th.json';
    
    // Register the locales
    countries.registerLocale(en);
    countries.registerLocale(th);
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    import { page } from '$app/stores';

    export let customer: any;

    let editForm: HTMLFormElement;
    let editModalOpen = false;
    let selectInstances: any = null;
    
    // State variables for handling messages
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    $: role = $page.data.role;
    $: isAgent = role === 'Agent';

    let formData: any;

    // Get country options based on current language
    $: countryOptions = Object.entries(countries.getNames($language || 'en'))
        .map(([code, name]) => ({ value: code, name }))
        .sort((a, b) => a.name.localeCompare(b.name));

    function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
        }
        return phone;
    }

    function openEditModal(customer: any) {
        selectInstances = { ...customer };
        editModalOpen = true;
        // Reset messages when opening modal
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';

        // Initialize formData with existing values or masked phone
        formData = {
            name: customer.name,
            email: customer.email,
            date_of_birth: customer.date_of_birth || '',
            phone: isAgent ? maskPhoneNumber(customer.phone) : customer.phone,
            address_line1: customer.address_line1 || '',
            address_line2: customer.address_line2 || '',
            city: customer.city || '',
            state_province_region: customer.state_province_region || '',
            zip_code: customer.zip_code || '',
            country: customer.country || ''
        };
    }

    function handleEditSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: editModalOpen,
        setModalOpen: (value: boolean) => editModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };
</script>

<Button class="text-gray-700 bg-white border hover:bg-gray-100 flex items-center gap-2 shadow-md" on:click={() => openEditModal(customer)}>
    {t('edit_customer')}
</Button>

<Modal bind:open={editModalOpen} size="lg" title="{t('edit_customer')}">
    <h2 slot="header">{t('edit_customer')}</h2>
    {#if selectInstances}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <form
            bind:this={editForm}
            action="?/update_customer"
            method="POST"
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleEditSubmit}
        >
            <input type="hidden" name="customer_id" value={customer.customer_id}>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Column: Personal Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">{t('personal_information')}</h3>
                    
                    <div>
                        <Label for="name" class="font-medium">{t('name')}</Label>
                        <Input id="name" name="name" type="text" bind:value={formData.name} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="email" class="font-medium">{t('email')}</Label>
                        <Input id="email" name="email" type="email" bind:value={formData.email} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="date_of_birth" class="font-medium">{t('date_of_birth')}</Label>
                        <Input id="date_of_birth" name="date_of_birth" type="date" bind:value={formData.date_of_birth} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="phone" class="font-medium">{t('phone_number')}</Label>
                        <Input id="phone" name="phone" type="text" bind:value={formData.phone} class="mt-1" />
                    </div>
                </div>

                <!-- Second Column: Address Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">{t('address_information')}</h3>
                    
                    <div>
                        <Label for="address_line1" class="font-medium">{t('address_line1')}</Label>
                        <Input id="address_line1" name="address_line1" type="text" bind:value={formData.address_line1} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="address_line2" class="font-medium">{t('address_line2')}</Label>
                        <Input id="address_line2" name="address_line2" type="text" bind:value={formData.address_line2} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="city" class="font-medium">{t('city')}</Label>
                        <Input id="city" name="city" type="text" bind:value={formData.city} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="state_province_region" class="font-medium">{t('state_province_region')}</Label>
                        <Input id="state_province_region" name="state_province_region" type="text" bind:value={formData.state_province_region} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="zip_code" class="font-medium">{t('zip_code')}</Label>
                        <Input id="zip_code" name="zip_code" type="text" bind:value={formData.zip_code} class="mt-1" />
                    </div>
                    
                    <div>
                        <Label for="country" class="font-medium">{t('country')}</Label>
                        <Select id="country" name="country" bind:value={formData.country} class="mt-1">
                            <!-- <option value="">{t('select_country')}</option> -->
                            {#each countryOptions as country}
                                <option value={country.value}>{country.name}</option>
                            {/each}
                        </Select>
                    </div>
                </div>
            </div>
        </form>
    {/if}
    <svelte:fragment slot="footer">
        <Button type="submit" color="blue" on:click={() => editForm.requestSubmit()}>{t('confirm')}</Button>
        <Button color="alternative" on:click={() => editModalOpen = false}>{t('cancel')}</Button>
    </svelte:fragment>
</Modal>