<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Input, Label } from 'flowbite-svelte';
	import { InfoCircleSolid, SearchOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let users: any[];
	export let loggedInUsername: string | null = null;
	export let loggedInRole: string | null = null;
	export let isDropDownItem: boolean = false;

	$: filteredUsers = users.filter((user) => {
		if (ticket.owner.name === 'System' && loggedInRole === 'Agent') {
			return user.username === loggedInUsername;
		} else if (
			ticket.owner.name === 'System' &&
			(loggedInRole === 'Supervisor' || loggedInRole === 'Admin')
		) {
			return user.roles !== 'System';
		} else if (loggedInRole === 'Supervisor' || loggedInRole === 'Admin') {
			return user.username !== ticket.owner.username && user.roles !== 'System';
		} else {
            return false;
        }
	});

	let ticketTransferOwnerForm: HTMLFormElement;
	let ticketTransferOwnerModalOpen = false;
	let transferingTicketOwner: any = null;
	let selectedUserId: string | number = '';
	let searchQuery = '';

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	// Filter users based on search query and sort by status
    	$: searchResults = filteredUsers.filter(user => {
        if (!searchQuery) return true;
        const lowerQuery = searchQuery.toLowerCase();
        return user.name?.toLowerCase().includes(lowerQuery) || 
            user.username?.toLowerCase().includes(lowerQuery) ||
            user.first_name?.toLowerCase().includes(lowerQuery) ||
            user.last_name?.toLowerCase().includes(lowerQuery);
    })
    .sort((a, b) => {
        // Sort by status: online first, then away, then offline
        const statusOrder = { online: 1, away: 2, offline: 3 };
        const statusA = statusOrder[a.status?.toLowerCase()] || 3; // Default to offline (3) if status is undefined
        const statusB = statusOrder[b.status?.toLowerCase()] || 3;
        return statusA - statusB;
    });

	function openTicketTransferOwnerModal(ticket: any) {
		transferingTicketOwner = { ...ticket };
		ticketTransferOwnerModalOpen = true;
		// Reset messages and search when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		searchQuery = '';
		selectedUserId = '';
	}

	function handleTicketTransferOwnerSubmit(event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	function selectUser(userId: string | number) {
		selectedUserId = userId;
	}

	$: enhanceOptions = {
		modalOpen: ticketTransferOwnerModalOpen,
		setModalOpen: (value: boolean) => (ticketTransferOwnerModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value)
	};

	// Function to get user initials for the avatar
	function getUserInitials(name: string): string {
		if (!name) return '';
		const parts = name.split(' ');
		if (parts.length >= 2) {
			return (parts[0][0] + parts[1][0]).toUpperCase();
		}
		return name[0]?.toUpperCase() || '';
	}

	// Function to get color for user avatar based on status
	function getAvatarColor(user: any): string {
		if (!user.status) return 'bg-gray-400'; // Default gray for unknown status
		
		switch (user.status.toLowerCase()) {
			case 'online':
				return 'bg-green-400';
			case 'away':
				return 'bg-yellow-400';
			case 'offline':
			default:
				return 'bg-gray-400';
		}
	}
	
	// Function to get status indicator class
	function getStatusIndicator(status: string): string {
		if (!status) return 'hidden';
		
		switch (status.toLowerCase()) {
			case 'online':
				return 'bg-green-400';
			case 'away':
				return 'bg-yellow-400';
			case 'offline':
			default:
				return 'hidden'; // Hide indicator for offline users
		}
	}

	// Function to check if a user is the current ticket owner
	function isCurrentOwner(userId: string | number): boolean {
		return transferingTicketOwner && transferingTicketOwner.owner && 
			   userId.toString() === transferingTicketOwner.owner.id.toString();
	}
</script>

{#if isDropDownItem}
	<Button
		color="none"
		class="w-full justify-start text-left hover:bg-gray-100"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		{t('transfer_ticket_ownership')}
	</Button>
{:else}
	<Button
		size="xs"
		class="bg-black text-white"
		color="black"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		{t('transfer_ticket')}
	</Button>
{/if}

<Modal bind:open={ticketTransferOwnerModalOpen} size="md" autoclose={false} class="w-full">
	<!-- Header -->
	<h2 slot="header" class="inline-flex items-center">
		<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('transfer_ticket_title')}
	</h2>

	{#if transferingTicketOwner}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{t(successMessage)}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{t(errorMessage)}
			</Alert>
		{/if}

		<!-- Display current owner information -->
		<div class="mb-4 p-3 bg-gray-100 rounded-lg shadow-md">
			<p class="font-medium text-gray-700">{t('current_owner')}</p>
			<div class="flex items-center mt-2">
				<div class="w-8 h-8 mr-3 {getAvatarColor(transferingTicketOwner.owner)} rounded-full flex items-center justify-center">
					<span class="text-white">{getUserInitials(transferingTicketOwner.owner.name)}</span>
				</div>
				<div>
					<p>{transferingTicketOwner.owner.name}</p>
					{#if transferingTicketOwner.owner.roles}
						<p class="text-xs text-gray-500">
                            ({transferingTicketOwner.owner.roles.map(role => role.name).join(", ")})
                        </p>
					{/if}
				</div>
			</div>
		</div>

		<form
			bind:this={ticketTransferOwnerForm}
			action="?/ticket_transfer_owner"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleTicketTransferOwnerSubmit}
		>
			<input type="hidden" name="ticket_id" value={transferingTicketOwner.id} />
			<div class="mb-4">
				<Label for="searchUser" class="mb-2 block font-semibold text-gray-700">
					{t('select_new_owner')}
				</Label>
				
				<!-- Search input -->
				<div class="relative mb-4">
					<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
						<SearchOutline class="w-5 h-5 text-gray-500" />
					</div>
					<Input 
						id="searchUser"
						placeholder={t('search')} 
						class="pl-10" 
						bind:value={searchQuery}
					/>
				</div>

				<!-- User list -->
				<div class="max-h-64 overflow-y-auto bg-gray-100 rounded-lg shadow-md">
					{#if searchResults.length === 0}
						<div class="flex items-center p-3 text-gray-500 border-b">
							<span>No assignee found</span> 
						</div>
					{:else}
						{#each searchResults as user}
							{@const isCurrent = isCurrentOwner(user.id)}
							{@const isOffline = !user.status || user.status.toLowerCase() === 'offline'}
							{@const isAway = user.status && user.status.toLowerCase() === 'away'}
							{@const isSelected = selectedUserId === user.id}
							<div 
								class="flex items-center p-3 hover:bg-gray-100 {isCurrent ? 'cursor-not-allowed' : 'cursor-pointer'} border-b 
								{isSelected && (isOffline || isAway) ? 'bg-red-50' : isSelected ? 'bg-blue-50' : ''}"
								on:click={() => !isCurrent && selectUser(user.id)}
							>
								<div class="relative">
									<div class="w-8 h-8 mr-3 {getAvatarColor(user)} rounded-full flex items-center justify-center">
										<span class="text-white">{getUserInitials(user.name)}</span>
									</div>
									<!-- Status indicator dot -->
									<div class="{getStatusIndicator(user.status)} w-2 h-2 absolute bottom-0 right-2 rounded-full border border-white"></div>
								</div>
								<div class="flex-1">
									<div class="flex items-center flex-wrap gap-1">
										<span>{user.name} ({user.first_name} {user.last_name})</span>
										{#if isCurrent}
											<span class="px-2 py-0.5 text-xs bg-gray-100 text-gray-700 rounded-full">currently</span>
										{/if}
										{#if isSelected && isOffline}
											<span class="px-2 py-0.5 text-xs bg-red-100 text-red-700 rounded-full">offline</span>
										{/if}
										{#if isSelected && isAway}
											<span class="px-2 py-0.5 text-xs bg-yellow-100 text-yellow-700 rounded-full">away</span>
										{/if}
									</div>
									{#if user.roles}
										<div class="text-xs text-gray-500">{t('role')} : {user.roles}</div>
									{/if}
								</div>
							</div>
						{/each}
					{/if}
				</div>
				
				<input type="hidden" name="new_owner_id" value={selectedUserId} />
			</div>
		</form>
	{/if}

	<!-- Warning message for offline or away users -->
	{#if selectedUserId}
		{@const selectedUser = users.find(user => user.id.toString() === selectedUserId.toString())}
		{#if selectedUser && selectedUser.status && selectedUser.status.toLowerCase() === 'offline'}
			<Alert color="red" class="mb-4">
				<span class="font-medium">{t('warning')}</span>{t('assign_offline_warning')}
			</Alert>
		{:else if selectedUser && selectedUser.status && selectedUser.status.toLowerCase() === 'away'}
			<Alert color="yellow" class="mb-4">
				<span class="font-medium">{t('warning')}</span>{t('assign_away_warning')}
			</Alert>
		{/if}
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<Button 
			color="dark" 
			on:click={() => ticketTransferOwnerForm.requestSubmit()}
			disabled={!selectedUserId || isCurrentOwner(selectedUserId)}
		>
            {t('confirm')}
		</Button>
		<Button color="none" on:click={() => (ticketTransferOwnerModalOpen = false)}>
            {t('cancel')}
        </Button>
	</svelte:fragment>
</Modal>