<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Select, Label, Textarea, Radio } from 'flowbite-svelte';
	import { InfoCircleSolid } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let priorities: any[];
	export let isDropDownItem: boolean = false;

	let ticketTransferOwnerForm: HTMLFormElement;
	let ticketTransferOwnerModalOpen = false;
	let transferingTicketOwner: any = null;
	let selectedUserId: string | number = '';

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	function openTicketTransferOwnerModal(ticket: any) {
		transferingTicketOwner = { ...ticket };
		ticketTransferOwnerModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	function handleTicketTransferOwnerSubmit(event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: ticketTransferOwnerModalOpen,
	//     setModalOpen: (value: boolean) => ticketTransferOwnerModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: ticketTransferOwnerModalOpen,
		setModalOpen: (value: boolean) => (ticketTransferOwnerModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value)
	};

	// Function to get priority color classes for the border/background
	function getPriorityColorClasses(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'border-gray-200 bg-gray-100 text-gray-700';
			case 'medium':
				return 'border-yellow-200 bg-yellow-200 text-yellow-700';
			case 'high':
				return 'border-orange-200 bg-orange-200 text-orange-700';
			case 'immediately':
				return 'border-red-200 bg-red-200 text-red-700';
			default:
				return 'border-gray-200 bg-gray-100 text-gray-700';
		}
	}

	// Function to get radio button color based on priority name
	function getRadioColor(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'gray';
			case 'medium':
				return 'yellow';
			case 'high':
				return 'orange';
			case 'immediately':
				return 'red';
			default:
				return 'gray';
		}
	}
</script>

{#if isDropDownItem}
	<Button
		color="none"
		class="w-full justify-start text-left hover:bg-gray-100"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		{t('priority_modal_title')}
	</Button>
{:else}
	<Button
		size="xs"
		class="bg-black text-white"
		color="black"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		<!-- Transfer Ownership -->
		{t('priority_modal_button')}
	</Button>
{/if}

<Modal bind:open={ticketTransferOwnerModalOpen} size="md" autoclose={false} class="w-full">
	<!-- Header -->
	<h2 slot="header" class="inline-flex items-center">
		<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('priority_modal_title')}
	</h2>

	{#if transferingTicketOwner}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}

		<form
			bind:this={ticketTransferOwnerForm}
			action="?/ticket_priority_change"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleTicketTransferOwnerSubmit}
		>
			<input type="hidden" name="ticket_id" value={transferingTicketOwner.id} />
			<div>
				<!-- Priority Selection with Radio Buttons -->
				<label class="mb-3 block text-sm font-medium text-gray-700">
					{t('priority_select_label')}
				</label>
				<div class="grid grid-cols-1 gap-3 md:grid-cols-2">
					{#each priorities as priority}
						<div class={`rounded-md border ${getPriorityColorClasses(priority.name)} shadow-md`}>
							<Radio 
								name="ticketPriority" 
								value={priority.id} 
								bind:group={selectedUserId} 
								color={getRadioColor(priority.name)}
								class="w-full p-4"
							>
								{priority.name}
							</Radio>
						</div>
					{/each}
				</div>

				<!-- Note Textarea -->
				<!-- <div class="mb-6">
                    <Label for="note" class="block mb-2">Note</Label>
                    <Textarea id="note" placeholder="optional..." rows="2" name="note" />
                </div> -->
				<input type="hidden" name="new_priority_id" value={selectedUserId} />
			</div>
		</form>
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<Button color="dark" on:click={() => ticketTransferOwnerForm.requestSubmit()}>{t('confirm')}</Button>
		<Button color="none" on:click={() => (ticketTransferOwnerModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>