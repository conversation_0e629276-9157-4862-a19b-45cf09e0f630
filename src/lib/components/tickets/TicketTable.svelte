<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { derived, writable } from 'svelte/store';

    import {
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Table
    } from 'flowbite-svelte';
    import { Tooltip } from 'flowbite-svelte';
    import {
        EditSolid,
        CaretDownSolid,
        CaretUpSolid
    } from 'flowbite-svelte-icons';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$src/lib/components/UI/ChangeTicketPriority.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';
    import { 
        displayDate, 
        timeAgo, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon 
    } from '$lib/utils';

    // Props for the component
    export let tickets = [];
    export let users = [];
    export let statuses = [];
    export let priorities = [];
    export let ticket_topics = [];
    export let loginUser = null;
    
    // Pagination state variables
    export let currentPage = 1;
    export let itemsPerPage = 10;
    export let totalPages = 1;
    export let paginatedDocuments = [];
    export let updateCurrentPage: (page: number) => void;

    // Sorting props
    export let sortColumn = 'status';
    export let sortDirection: 'asc' | 'desc' = 'asc';
    export let sortBy: (column: string, direction: string) => void;

    // Track sort state changes
    $: currentSortColumn = sortColumn;
    $: currentSortDirection = sortDirection;

    // Create reactive stores for sort state
    $: sortColumnStore = writable(sortColumn);
    $: sortDirectionStore = writable(sortDirection);
    
    // Update stores when props change
    $: {
        sortColumnStore.set(sortColumn);
        sortDirectionStore.set(sortDirection);
    }

    // Helper function to show sort icon
    $: getCaretIcon = (column: string) => {
        if (currentSortColumn === column) {
            // console.log('getCaretIcon');
            return currentSortDirection === 'asc' ? CaretUpSolid : CaretDownSolid;
        }
        return null;
    }

    function handleSort(column: string) {
        const newDirection = (currentSortColumn === column && currentSortDirection === 'asc') ? 'desc' : 'asc';
        sortBy(column, newDirection);
    }
</script>

<div class="w-full overflow-x-auto">
    <!-- <Table shadow class="table-fixed min-w-full"> -->
    <Table shadow > 
        <TableHead>
            <TableHeadCell class="w-16 cursor-pointer" on:click={() => handleSort('id')}>
                <div class="flex items-center justify-start">
                    {t('table_no')}
                    <svelte:component this={getCaretIcon('id')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('status')}>
                <div class="flex items-center justify-start">
                    {t('table_status')}
                    <svelte:component this={getCaretIcon('status')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('priority')}>
                <div class="flex items-center justify-start">
                    {t('table_priority')}
                    <svelte:component this={getCaretIcon('priority')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 text-center cursor-pointer" on:click={() => handleSort('sentiment')}>
                <div class="flex items-center justify-center">
                    {t('table_sentiment')}
                    <svelte:component this={getCaretIcon('sentiment')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-40 cursor-pointer" on:click={() => handleSort('customer')}>
                <div class="flex items-center justify-start">
                    {t('table_customer')}
                    <svelte:component this={getCaretIcon('customer')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('agent')}>
                <div class="flex items-center justify-start">
                    {t('table_agent')}
                    <svelte:component this={getCaretIcon('agent')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>
            
            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('created_on')}>
                <div class="flex items-center justify-start">
                    {t('table_time')}
                    <svelte:component this={getCaretIcon('created_on')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-40">{t('table_actions')}</TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('updated_on')}>
                <div class="flex items-center justify-start">
                    {t('table_updated_on')}
                    <svelte:component this={getCaretIcon('updated_on')} class="inline-block h-4 w-4 ml-1" />
                </div>
            </TableHeadCell>
        </TableHead>
        <TableBody tableBodyClass="divide-y"> 
            {#if paginatedDocuments.length === 0}
                <TableBodyRow>
                    <TableBodyCell colspan={9} class="text-center py-4 text-gray-500">
                        {t('table_no_ticket')}
                    </TableBodyCell>
                </TableBodyRow>
            {:else}
                {#each paginatedDocuments as ticket}
                    <TableBodyRow>
                        <TableBodyCell>
                            <a
                                href="/monitoring/{ticket.id}"
                                class="flex items-center text-blue-600 hover:underline py-2"
                            >
                                {ticket.id}<EditSolid class="h-4 w-4 ml-1" />
                            </a>
                        </TableBodyCell>
                        <TableBodyCell>
                            <div class="flex justify-start">
                                <span class={`${getStatusClass(ticket.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
                                    {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                                </span>
                            </div>
                        </TableBodyCell>
                        
                        <TableBodyCell>
                            <div class="flex justify-start">  
                                <span class={`${getPriorityClass(ticket.priority.name)} p-2 rounded-md text-sm w-24`}>
                                    {ticket.priority.name ?? "-"}
                                </span>
                            </div>                        
                        </TableBodyCell>

                        <TableBodyCell>
                            <div class="flex justify-center"> 
                                <div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
                                    <img
                                        src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
                                        alt={ticket.latest_analysis?.sentiment}
                                        class="w-5 h-5"
                                    />
                                    <Tooltip>{ticket.latest_analysis?.sentiment ?? t('table_unclassified')}</Tooltip>
                                </div>
                            </div>
                        </TableBodyCell>

                        <TableBodyCell>
                            {#if ticket.customer.name}
                                <div class="text-sm font-medium">{ticket.customer.name}</div>
                                {#if ticket.customer.email}
                                    <div class="text-xs text-gray-500 truncate">{ticket.customer.email}</div>
                                {/if}
                            {:else if ticket.customer.line_user && ticket.customer.line_user.display_name}
                                <div class="text-sm font-medium">{ticket.customer.line_user.display_name}</div>
                                {#if ticket.customer.line_user.user_id}
                                    <div class="text-xs text-gray-500 truncate">ID: {ticket.customer.line_user.user_id}</div>
                                {/if}
                            {:else}
                                <div class="text-sm">{t('table_unknown')}</div>
                            {/if}
                        </TableBodyCell>
                        <TableBodyCell>
                            <div class="text-sm">{ticket.owner.name ? ticket.owner.name : '-'}</div>
                            <div class="text-xs text-gray-500">
                                {ticket.owner?.roles?.length
                                  ? ticket.owner.roles.map(role => role.name).join(', ')
                                  : '-'}
                            </div>            
                        </TableBodyCell>
                        <TableBodyCell>
                            <span class="text-sm">{timeAgo(ticket.updated_on, ticket.status)}</span>
                        </TableBodyCell>

                        <TableBodyCell>
                            <div class="action-container">
                                <TransferTicketOwner 
                                    {ticket} 
                                    {users}
                                    loggedInUsername={loginUser.username}
                                    loggedInRole={loginUser.roles[0].name} 
                                />
                                <ChangeTicketPriority {ticket} {priorities} />
                                <ChangeTicketStatus {ticket} {statuses} {ticket_topics}/>
                            </div>
                        </TableBodyCell>

                        <TableBodyCell>
                            <div class="text-sm">{displayDate(ticket.updated_on).date}</div>
                            <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
                        </TableBodyCell>
                    </TableBodyRow>
                {/each}
            {/if}
        </TableBody>
    </Table>
</div>

<!-- Pagination Layout -->	
<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />

<style>
    /* Add custom styles to ensure consistent appearance */
    :global(.tooltip) {
        z-index: 50;
    }
    
    /* Responsive action buttons */
    .action-container {
        display: grid;
        gap: 0.25rem;
    }
    
    /* Large screens - 3 columns */
    @media (min-width: 1024px) {
        .action-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    /* Medium screens - 2 columns */
    @media (min-width: 768px) and (max-width: 1023px) {
        .action-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    /* Small screens - 1 column */
    @media (max-width: 767px) {
        .action-container {
            grid-template-columns: 1fr;
        }
    }
    
    /* Ensure table doesn't break layout on small screens */
    @media (max-width: 1024px) {
        :global(table) {
            width: max-content !important;
        }
    }
</style>