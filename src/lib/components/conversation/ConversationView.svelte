<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	export let customerId: number;
	export let platformId: number;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];
	export let access_token: string;

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';
	
	$: platformMessages = $conversationStore.messages.get(platformId) || [];
	$: messages = platformMessages;
	
	// React to platformId or customerId changes
	$: if (platformId && customerId) {
		loadConversationForPlatform(customerId, platformId);
	}
	
	onDestroy(() => {
		disconnectWebSocket();
	});
	
	async function loadConversationForPlatform(custId: number, platId: number) {
		// Disconnect from previous WebSocket if any
		disconnectWebSocket();
		
		// Clear previous messages to show loading state
		messages = [];
		
		try {
			loading = true;
			
			// Load platform info
			const platformResponse = await fetch(
				`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
				{
					credentials: 'include'
				}
			);
			
			if (platformResponse.ok) {
				const platformData = await platformResponse.json();
				
				// Handle both single result and array of results
				const platform = Array.isArray(platformData.results) 
					? platformData.results[0] 
					: platformData;
				
				customerName = platform.display_name || platform.platform_username || 'Unknown User';
				channelName = platform.channel_name || platform.platform;
			}
			
			// Load messages using the store's built-in method
			await conversationStore.loadConversation(custId, platId);
			
			// Get the loaded messages to check for unread ones
			const loadedMessages = platformMessages;
			
			// Mark messages as read
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);
			
			if (unreadMessageIds.length > 0) {
				markMessagesAsRead(unreadMessageIds);
			}
			
			// Connect WebSocket for this platform
			connectWebSocket(custId, platId);
			
		} catch (error) {
			console.error('Error loading conversation:', error);
		} finally {
			loading = false;
		}
	}
	
	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}
	
	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}
	
	async function markMessagesAsRead(messageIds: number[]) {
		try {
			await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
				{
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						message_ids: messageIds
					})
				}
			);
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}
	
	async function handleSendMessage(event: CustomEvent<{ content: string; type: string }>) {
		const { content, type } = event.detail;
		
		try {
			const response = await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/`,
				{
					credentials: 'include',
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						message: content,
						message_type: type,
					}),
				}
			);
			
			if (response.ok) {
				const newMessage = await response.json();
				conversationStore.addMessage(platformId, newMessage);
			}
		} catch (error) {
			console.error('Error sending message:', error);
		}
	}
	
	async function handleLoadMore() {
		if (loading || !messages.length) return;
		
		const oldestMessage = messages[0];
		if (oldestMessage) {
			try {
				loading = true;
				const response = await fetch(
					`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${oldestMessage.id}&limit=50`,
					{
						credentials: 'include'
					}
				);
				
				if (response.ok) {
					const data = await response.json();
					if (data.messages && data.messages.length > 0) {
						// Prepend older messages
						conversationStore.prependMessages(platformId, data.messages);
					}
				}
			} catch (error) {
				console.error('Error loading more messages:', error);
			} finally {
				loading = false;
			}
		}
	}
</script>

<div class="h-full flex flex-col">
	<ConversationHeader 
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{users} 
		{priorities} 
		{statuses} 
		{topics}
		{access_token}
	/>
	
	<MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/>
	
	<MessageInput 
		on:send={handleSendMessage}
		disabled={loading}
	/>
</div>