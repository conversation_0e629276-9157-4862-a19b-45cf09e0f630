<script lang="ts">
    import { Button, Modal, Input, Label, Radio, Toast } from "flowbite-svelte";
    import { CheckCircleSolid, ClipboardSolid } from "flowbite-svelte-icons";
    import { fly } from 'svelte/transition';
    import { t } from '$lib/stores/i18n';

    export let showModal = false;
    export let connectionSettings;

    let connectionName = "";
    let channelId = "";
    let channelSecret = "";
    let channelAccessToken = "";
    let lineProviderId = "";
    let lineProviderName = "";
    let isVerified = false;
    
    // Toast state
    let showToast = false;
    let toastMessage = "";
    
    // Webhook URL (this would typically come from your backend)
    let webhookUrl = "https://api.yourapp.com/webhook/line";

    function handleConnect() {
        // Handle the connection logic here
        const connectionData = {
            connectionName,
            channelId,
            channelSecret,
            channelAccessToken,
            lineProviderId,
            lineProviderName,
            isVerified
        };
        
        console.log('Saving LINE Business:', connectionData);
        
        // Show success toast
        toastMessage = t('connection_saved_successfully');
        showToast = true;
        
        // Auto hide toast after 3 seconds
        setTimeout(() => {
            showToast = false;
        }, 3000);
        
        // Close modal after connection
        showModal = false;
        
        // You can emit an event or call a callback here
        // dispatch('connect', connectionData);
    }

    function handleCancel() {
        showModal = false;
        // Reset form
        connectionName = "";
        channelId = "";
        channelSecret = "";
        channelAccessToken = "";
        lineProviderId = "";
        lineProviderName = "";
        isVerified = false;
    }
    
    function copyWebhookUrl() {
        navigator.clipboard.writeText(webhookUrl);
        toastMessage = t('webhook_url_copied');
        showToast = true;
        setTimeout(() => {
            showToast = false;
        }, 2000);
    }
</script>

<Modal bind:open={showModal} size="lg" autoclose={false} title={t('connect_line_business')} class="w-full">
    <p class="text-base leading-relaxed text-gray-500 mb-4">
        {t('line_business_configuration_description')}
    </p>
    
    <div class="space-y-6">
        <!-- Connection Name -->
        <div>
            <Label for="connection-name" class="block mb-2 text-sm font-medium text-gray-900">
                {t('connection_name')}
            </Label>
            <Input
                id="connection-name"
                bind:value={connectionName}
                placeholder={t('connection_name_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('connection_name_description')}</p>
        </div>

        <!-- Channel ID -->
        <div>
            <Label for="channel-id" class="block mb-2 text-sm font-medium text-gray-900">
                {t('channel_id')}
            </Label>
            <Input
                id="channel-id"
                bind:value={channelId}
                placeholder={t('channel_id_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('channel_id_description')}</p>
        </div>

        <!-- Channel Secret -->
        <div>
            <Label for="channel-secret" class="block mb-2 text-sm font-medium text-gray-900">
                {t('channel_secret')}
            </Label>
            <Input
                id="channel-secret"
                type="password"
                bind:value={channelSecret}
                placeholder={t('channel_secret_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('channel_secret_description')}</p>
        </div>

        <!-- Channel Access Token -->
        <div>
            <Label for="channel-access-token" class="block mb-2 text-sm font-medium text-gray-900">
                {t('channel_access_token')}
            </Label>
            <Input
                id="channel-access-token"
                type="password"
                bind:value={channelAccessToken}
                placeholder={t('channel_access_token_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('channel_access_token_description')}</p>
        </div>

        <!-- LINE Provider ID -->
        <div>
            <Label for="line-provider-id" class="block mb-2 text-sm font-medium text-gray-900">
                {t('line_provider_id')}
            </Label>
            <Input
                id="line-provider-id"
                bind:value={lineProviderId}
                placeholder={t('line_provider_id_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('line_provider_id_description')}</p>
        </div>

        <!-- LINE Provider Name -->
        <div>
            <Label for="line-provider-name" class="block mb-2 text-sm font-medium text-gray-900">
                {t('line_provider_name')}
            </Label>
            <Input
                id="line-provider-name"
                bind:value={lineProviderName}
                placeholder={t('line_provider_name_placeholder')}
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
            />
            <p class="mt-1 text-sm text-gray-500">{t('line_provider_name_description')}</p>
        </div>

        <!-- Webhook Settings - Show only when Channel ID is filled -->
        <div>
            <div class="flex items-center gap-2">
                <p class="text-sm text-gray-500">{t('webhook_settings_description')}</p>
                {#if channelId}
                    <Button 
                        color="alternative" 
                        size="sm" 
                        on:click={copyWebhookUrl}
                        class="px-2 py-1 text-sm"
                    >
                        <ClipboardSolid class="w-4 h-4 mr-1" />
                        {t('copy')}
                    </Button>
                {/if}
            </div>
        </div>

        <!-- Verified Radio Button -->
        <!-- <div>
            <Label class="block mb-2 text-sm font-medium text-gray-900">
                {t('verification_status')}
            </Label>
            <div class="flex items-center space-x-4">
                <Radio bind:group={isVerified} value={false} class="text-blue-600 focus:ring-blue-500">
                    {t('not_verified')}
                </Radio>
                <Radio bind:group={isVerified} value={true} class="text-blue-600 focus:ring-blue-500">
                    {t('verified')}
                </Radio>
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('verification_status_description')}</p>
        </div> -->
    </div>
    
    <svelte:fragment slot="footer">
        <Button color="alternative" on:click={handleCancel}>
            {t('cancel')}
        </Button>
        <Button color="dark" on:click={handleConnect}>
            {t('save')}
        </Button>
    </svelte:fragment>
</Modal>

<!-- Success Toast -->
{#if showToast}
    <Toast
        color="green"
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus={showToast}
        class="fixed left-1/2 top-20 -translate-x-1/2 transform z-50"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}