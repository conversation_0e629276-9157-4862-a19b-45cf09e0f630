<script lang="ts" context="module">
	import { toastStore } from '$lib/stores/toastStore';

	interface EnhanceOptions {
		modalOpen: boolean;
		setModalOpen?: (value: boolean) => void;
		setPending?: (value: boolean) => void;
		setShowSuccessMessage?: (value: boolean) => void;
		setSuccessMessage?: (value: string) => void;
		setShowErrorMessage?: (value: boolean) => void;
		setErrorMessage?: (value: string) => void;
		// New optional properties for enhanced behavior
		useToastOnSuccess?: boolean;
		closeModalOnSuccess?: boolean;
	}

	export const handleEnhance = (options: EnhanceOptions) => {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			// TODO - Delete this
			// console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result - ${JSON.stringify(result)}`)
			// console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result type - ${result.type}`)

			// Set pending state if the option is provided
			options.setPending?.(true);

			if (result.type === 'failure') {
				options.setErrorMessage?.(result.data?.error || 'Status : Operation failed');
				// Don't close modal on error
			} else if (result.type === 'success') {
				const successMessage = result.data.res_msg || 'Status : Operation success';

				if (options.useToastOnSuccess) {
					toastStore.add(successMessage, 'success');
				} else {
					// Existing behavior for backward compatibility
					options.setShowSuccessMessage?.(true);
					options.setSuccessMessage?.(successMessage);
				}

				if (options.closeModalOnSuccess) {
					options.setModalOpen?.(false);
				}
			}
			// Update the page data
			await update();

			// Reset pending state if the option is provided
			options.setPending?.(false);
		};
	};
</script>
