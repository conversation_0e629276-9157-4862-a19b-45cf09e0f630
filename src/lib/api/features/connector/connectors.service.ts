// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { RoleInterface, RoleResponse } from "../../types/role";
import { ApiError } from "../../client/errors";

export interface LineConnectorInput {
    name: string;
    channel_id: string;
    channel_secret: string;
    channel_access_token: string;
    line_provider_id: string;
    line_provider_name: string;
}

export interface LineConnectorUpdateInput {
    name: string;
    channel_id: string;
    channel_secret: string;
    channel_access_token: string;
    line_provider_id: string;
}

export interface LineChannelStatusInput {
    action: "enable" | "disable";
    reason: string;
}

export class ConnectorService {
    private baseUrl = `${getBackendUrl()}`;

    async createLineConnector(token: string, connectorData: LineConnectorInput): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/connectors/connect/line/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(connectorData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error creating LINE connector:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to create LINE connector'
            };
        }
    }

    async verifyLineConnector(token: string, backend_LINE_connector_id: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/connectors/channels/line/${backend_LINE_connector_id}/test/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error verifying LINE connector:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to verify LINE connector'
            };
        }
    }

    async getAllLineConnectors(token: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/connectors/channels/line/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching all LINE connectors:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch LINE connectors'
            };
        }
    }

    async getLineConnectorById(token: string, backend_LINE_channel_id: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/connectors/channels/line/${backend_LINE_channel_id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching LINE connector by ID:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch LINE connector'
            };
        }
    }

    async updateLineConnectorById(token: string, backend_LINE_channel_id: string, connectorData: LineConnectorUpdateInput): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/connectors/channels/line/${backend_LINE_channel_id}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(connectorData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error updating LINE connector:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to update LINE connector'
            };
        }
    }

    async disableLineChannel(token: string, backend_LINE_channel_id: string, reason: string = "Maintenance required"): Promise<RoleResponse> {
        try {
            const statusData: LineChannelStatusInput = {
                action: "disable",
                reason: reason
            };

            const response = await fetch(`${this.baseUrl}/connectors/channels/line/${backend_LINE_channel_id}/status/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(statusData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error disabling LINE channel:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to disable LINE channel'
            };
        }
    }

    async enableLineChannel(token: string, backend_LINE_channel_id: string, reason: string = "Maintenance completed"): Promise<RoleResponse> {
        try {
            const statusData: LineChannelStatusInput = {
                action: "enable",
                reason: reason
            };

            const response = await fetch(`${this.baseUrl}/connectors/channels/line/${backend_LINE_channel_id}/status/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(statusData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules: response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error enabling LINE channel:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to enable LINE channel'
            };
        }
    }
}