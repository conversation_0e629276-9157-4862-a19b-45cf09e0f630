<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import {
		Breadcrumb,
		BreadcrumbItem,
        Tabs, 
        TabItem,
        Button,
        Indicator
	} from 'flowbite-svelte';

	import { enhance } from '$app/forms';
	// import { formatTimestamp } from '$lib/utils';
	import type { PageData } from './$types';
	import { getColorClass } from '$lib/utils';

	import type { CustomerInterface } from '$src/lib/api/types/customer';
	// import { Button as FlowbiteButton, Modal } from 'flowbite-svelte';
    // import { PUBLIC_BACKEND_URL } from '$env/static/public';
    // import { env as publicEnv } from '$env/dynamic/public';
    import { getBackendUrl } from '$src/lib/config';


    import Memory from '$src/lib/components/customer/detail/memory.svelte';
    import Notes from '$src/lib/components/customer/detail/notes.svelte';
    import Policy from '$src/lib/components/customer/detail/policy.svelte';
    import Ticket from '$src/lib/components/customer/detail/ticket.svelte';
    import Profile from '$src/lib/components/customer/detail/profile.svelte';
    import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
    import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';

	export let data: PageData;

	$: ({ customer, customer_notes, customer_policies, customer_tickets, customer_tags, customer_memory, access_token} = data);

    function exportCustomerChat() {
        const url = `${getBackendUrl()}/customer/api/customers/${customer.customer_id}/messages/`;
        console.log(url);
        const bodyData = { "format": "zip" };

        fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
    }

    // let defaultModal = false;
    // // Note management variables
    // let editModal = false;
    // let deleteModal = false;
    // let editNote;
    // let deleteNoteId;

    // console.log(data.customer)
	// console.log(data.customer_notes);
    // console.log(data.customer_policies)
    // console.log(data.customer_tickets)

	// import AddNote from '$lib/components/UI/UserNote.svelte';

	// Define customer information fields
	// const customerFields = [
	// 	{
	// 		key: 'ID',
	// 		value: (customer: CustomerInterface) => customer.customer_id
	// 	},
	// 	{
	// 		key: 'Name',
	// 		value: (customer: CustomerInterface) => customer.name
	// 	},
	// 	{
	// 		key: 'Email',
	// 		value: (customer: CustomerInterface) => customer.email || 'N/A'
	// 	},
	// 	{
	// 		key: 'Age',
	// 		value: (customer: CustomerInterface) => customer.age || 'N/A'
	// 	},
	// 	{
	// 		key: 'Phone',
	// 		value: (customer: CustomerInterface) => customer.phone || 'N/A'
	// 	},
    //     {
	// 		key: 'Tag',
	// 		value: (customer: CustomerInterface) => customer.tags || 'N/A'
	// 	},
	// ];

	// let notes: Note[] = [];
	// let newNote = '';

	// Function to handle adding a note
	// const addNote = () => {
	// 	if (newNote.trim()) {
	// 		const note: Note = {
	// 			id: notes.length + 1,
	// 			content: newNote,
	// 			author: '.Gun', // This would typically come from the logged-in user
	// 			timestamp: new Date()
	// 		};
	// 		notes = [note, ...notes]; // Add new note at the beginning
	// 		newNote = ''; // Clear the input
	// 	}
	// };

	// Function to delete a note
	// const deleteNote = (noteId: number) => {
	// 	notes = notes.filter((note) => note.id !== noteId);
	// };
</script>

<svelte:head>
	<title>Customer Details</title>
</svelte:head>

<div class="min-h-screen bg-white rounded-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <!--Breadcrumb -->
        <div>
            <Breadcrumb aria-label="Default breadcrumb example">
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem href="/customer">
                    <span class="text-gray-400">{t('customers')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('detail')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
        </div>
        <!--Customer Header -->
        <!-- <div class="mb-8 flex items-center justify-between">
            <h1 class="text-3xl font-bold text-gray-900">
                Customer No. {customer.customer_id} : {customer.name
                    ? customer.name
                    : customer.line_user
                        ? customer.line_user.display_name
                        : '-'}
            </h1>
        </div> -->
        
        <!--Customer Informations and Notes -->
        <div class="flex flex-col md:flex-row gap-5 mt-4">
            <!-- Left side: Customer Profile and Notes -->
            <div class="md:col-span-1 space-y-4">
                <Profile {customer} {customer_tags}/>

                <div class="flex flex-col space-y-3">
                    <CustomerEdit {customer} />
                    <CustomerTag {customer} {customer_tags}/>
                    <Button class="text-gray-700 bg-white border hover:bg-gray-100 flex items-center gap-2 shadow-md" on:click={exportCustomerChat}>
                        {t('export_customer_conversations')}
                    </Button>
                </div>
            </div>
            
            <!-- Right side: Policy and Ticket Info -->
            <!-- <div class="w-full md:w-3/4 rounded-lg bg-gray-100 p-2 text-center"> -->
            <div class="md:col-span-3 border rounded-lg bg-white shadow-md p-4 w-full">
                <Tabs tabStyle="underline">
                    <TabItem open title="{t('tickets')}">
                        <Ticket {customer_tickets}/>
                    </TabItem>

                    <TabItem title="{t('policies')}">
                        <Policy {customer_policies} />

                    </TabItem>

                    <TabItem title="{t('notes')}">
                        <Notes {access_token} {customer} {customer_notes}/>
                    </TabItem>

                    <TabItem title="{t('memories')}">
                        <Memory memories={customer_memory} />
                    </TabItem>
                </Tabs>
            </div>
        </div>
    </div>
</div>
