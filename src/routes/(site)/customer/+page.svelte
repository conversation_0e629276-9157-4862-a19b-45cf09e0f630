<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { page } from '$app/stores';

	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Input,
		Tooltip,
		Indicator,
	} from 'flowbite-svelte';
	import { PhoneSolid } from 'flowbite-svelte-icons';
	import {
		UsersSolid,
		CaretDownSolid,
		CaretUpSolid,
		EditSolid,
		SearchOutline
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
    import { formatTimestamp, displayDate } from '$lib/utils';
	import { getColorClass } from '$lib/utils';

	export let data: PageData;
	$: ({ customers, error } = data);

	console.log(customers)


    $: role = $page.data.role;
    $: isAgent = role === 'Agent';

	function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
        }
        return phone;
    }

	let searchQuery = '';
	let sortColumn: keyof (typeof customers)[0] = 'customer_id';
	let sortDirection: 'asc' | 'desc' = 'asc';

	let searchId = '';
	let searchPhone = '';
	let searchEmail = '';

	$: filteredCustomers = customers?.filter((customer) => {
        currentPage = 1
		const customer_name = customer.name;
		const searchMatch = 
			customer_name.toLocaleLowerCase().includes(searchQuery.toLowerCase()) || 
			String(customer.customer_id).toLowerCase().includes(searchQuery.toLowerCase()) || 
			customer.phone && customer.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
			customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase());

		return searchMatch
	});

	function sortBy(column: keyof (typeof customers)[0]) {
		if (sortColumn === column) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortColumn = column;
			sortDirection = 'asc';
		}
	}

	function filterAll(item: any, term: string) {
		const t = term.toLowerCase();
		return [
			String(item.customer_id), // ID
			item.name ?? item.line_user?.display_name, // Name
			item.phone, // Phone
			item.main_interface?.name, // Platform
			item.email, // Email
			displayDate(item.created_on) // Created On
		]
			.filter(Boolean)
			.some((field) => field.toLowerCase().includes(t));
	}

	function compare(a: any, b: any) {
		let av = a[sortColumn],
			bv = b[sortColumn];
		if (sortColumn === 'created_on') {
			av = new Date(av).getTime();
			bv = new Date(bv).getTime();
		}
		if (typeof av === 'number' && typeof bv === 'number') {
			return sortDirection === 'asc' ? av - bv : bv - av;
		}
		return sortDirection === 'asc'
			? String(av).localeCompare(String(bv))
			: String(bv).localeCompare(String(av));
	}

	// Calculate statistics
	$: totalCustomers = filteredCustomers?.length || 0;

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

	$: totalPages = Math.ceil(Math.max((filteredCustomers ?? []).length, 1) / itemsPerPage);
	$: paginatedCustomers = (filteredCustomers ?? []).slice(0, itemsPerPage);

	function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedCustomers = filteredCustomers.slice(idx, Math.min(idx + itemsPerPage, filteredCustomers.length));
	}

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		updatePagination();
	}
	
	// Format tag name to capitalize first letter
	function formatTagName(tag: string): string {
		return tag.charAt(0).toUpperCase() + tag.slice(1);
	}

    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';

</script>

<svelte:head>
	<title>Customers</title>
</svelte:head>

<div class="flex h-screen">
	<div class="w-full overflow-y-auto bg-white p-8">

        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
                <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
                <span class="text-gray-700">{t('customers')}</span>
            </BreadcrumbItem>
        </Breadcrumb>

		<div class="mb-6">
			<h2 class="text-2xl font-bold">{t('customers')}</h2>
			<p class="text-gray-600">{totalCustomers} {t('customers')}</p>
		</div>

		<div class="mb-4 flex w-full justify-end">
			<div class="relative lg:w-1/3">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="searchBar"
					type="text"
					placeholder={t('customers_search_placeholder')}
					bind:value={searchQuery}
					class={`block w-full rounded-lg border bg-white py-2.5 pl-10 focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		{#if error}
			<p class="text-red-600">{error}</p>
		{:else}
			<Table sort={compare} hoverable shadow class="table-fixed w-full">
				<TableHead>
					<TableHeadCell class="w-[70px]" on:click={() => sortBy('customer_id')}>
						<div class="flex items-center">
							{t('table_no')}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[250px]"  on:click={() => sortBy('name')}>
						<div class="flex items-center">
							{t('table_name')}
							{#if sortColumn === 'name'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

                    <TableHeadCell class="w-[150px]" on:click={() => sortBy('tag')}>{t('table_tag')}</TableHeadCell>

					<TableHeadCell class="w-[180px]" on:click={() => sortBy('phone')}>{t('table_phone')}</TableHeadCell>

					<TableHeadCell class="w-[150px]" on:click={() => sortBy('main_interface')}>{t('table_platform')}</TableHeadCell>

					<TableHeadCell class="w-[280px]" on:click={() => sortBy('email')}>{t('table_email')}</TableHeadCell>

					<!-- <TableHeadCell class="w-[150px]" on:click={() => sortBy('created_on')}>{t('table_created_on')}</TableHeadCell> -->
				</TableHead>

				<TableBody>
                    {#if paginatedCustomers.length === 0}
                        <TableBodyRow>
                            <TableBodyCell colspan={9} class="text-center py-4 text-gray-500">
                                {t('table_no_data')}
                            </TableBodyCell>
                        </TableBodyRow>
                    {:else}
                        {#each paginatedCustomers as item}
                            <TableBodyRow slot="row">
                                <TableBodyCell>
                                    <a
                                        href={`/customer/${item.customer_id}`}
                                        class="flex items-center text-blue-600 hover:underline py-2"
                                    >
                                        {item.customer_id}
                                        <EditSolid class="ml-1 h-4 w-4" />
                                    </a>
                                </TableBodyCell>

                                <TableBodyCell>
                                    <span class="break-words">
                                        <!-- {item.name ? item.name : item.line_user ? item.name : '-'} -->
										{item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : item.name || '-'}
                                    </span>
                                </TableBodyCell>

                                <TableBodyCell>
                                    {#if item.tags && item.tags.length > 0}
                                        {#if item.tags.length === 1}
                                            <!-- Show single tag badge -->
                                            <span
                                                class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
                                            >
												<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(item.tags[0].color)}`}></span> -->
												<Indicator size="sm" class={`mr-1 ${getColorClass(item.tags[0].color)} inline-block`} />
                                                {formatTagName(item.tags[0].name)}
                                            </span>
                                        {:else}
                                            <!-- Show badge with count -->
                                            <div class="relative inline-block">
                                                <span
                                                    class="text-white-700 inline-flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm cursor-pointer"
                                                    data-popover-target="popover-tags-{item.customer_id}"
                                                >
                                                    <span class="flex items-center gap-1">
                                                        <!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
														<span class="relative flex -space-x-1">
															{#each item.tags.slice(0, 3) as tag, i (tag.name)}
																<!-- <span
																	class={`inline-block w-2 h-2 rounded-full ${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																></span> -->
																<Indicator 
																	size="sm" 
																	class={`${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
                                                        {item.tags.length} {t('labels')}
                                                    </span>
                                                </span>
                                                
                                                <!-- Tooltip/popover for all tags -->
                                                <Tooltip triggeredBy="[data-popover-target='popover-tags-{item.customer_id}']">
                                                    <div class="py-1 px-2 max-w-xs">
                                                        <ul class="space-y-1">
                                                            {#each item.tags as tag}
                                                                <li class="flex items-center gap-1">
																	<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(tag.color)}`}></span> -->
																	<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
                                                                    {formatTagName(tag.name)}
                                                                </li>
                                                            {/each}
                                                        </ul>
                                                    </div>
                                                </Tooltip>
                                            </div>
                                        {/if}
                                    {:else}
                                        -
                                    {/if}
                                </TableBodyCell>

                                <TableBodyCell>{item.phone ? maskPhoneNumber(item.phone) : '-'}</TableBodyCell>
                                <TableBodyCell>
                                    {#if item.main_interface?.name === 'LINE'}
                                        <div class="flex items-center">
                                            <img src="/images/line-icon.png" alt="LINE Icon" class="mr-2 h-5 w-5" />
                                            {item.main_interface.name}
                                        </div>
                                    {:else if item.main_interface?.name === 'FACEBOOK'}
                                        <div class="flex items-center">
                                            <img src="/images/facebook-icon.png" alt="Facebook Icon" class="mr-2 h-5 w-5" />
                                            {item.main_interface.name}
                                        </div>
                                    {:else if item.main_interface?.name === 'TELEPHONE'}
                                        <div class="flex items-center">
                                            <PhoneSolid class="mr-2 h-5 w-5" />
                                            {item.main_interface.name}
                                        </div>
                                    {:else}
                                        {item.main_interface?.name || '-'}
                                    {/if}
                                </TableBodyCell>

                                <TableBodyCell>
                                    <span class="break-words">
                                        {item.email ?? '-'}
                                    </span>
                                </TableBodyCell>
                                <!-- <TableBodyCell>
                                    <div>{displayDate(item.created_on).date}</div>
                                    <div>{displayDate(item.created_on).time}</div>
                                </TableBodyCell> -->
                            </TableBodyRow>
                        {/each}
                    {/if}
				</TableBody>
			</Table>

            <!-- Pagination Layout -->
			<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
		{/if}
	</div>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>