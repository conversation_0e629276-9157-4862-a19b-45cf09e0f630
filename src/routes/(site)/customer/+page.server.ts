import type { PageServerLoad } from "./$types";
import { services } from "$lib/api/features";
import { error, fail, redirect } from "@sveltejs/kit";
import type { Actions } from "./$types";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    if (!access_token) {
        return {
            customers: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response = await services.customers.getAll(access_token);

            if (response.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }
            // console.log(response.customers)
            // response.customers.forEach((user, index) => {
            //     console.log(`\nUser ${index + 1}: ${user.name}`);
            //     console.log("Tags:", JSON.stringify(user.tags, null, 2));
            // });

            return {
                customers: response.customers?.results || [], // <-- Extract the results array
                totalCount: response.customers?.count || 0,   // <-- Optional: also return total count
                pagination: {
                    next: response.customers?.next,
                    previous: response.customers?.previous
                }
            };
        } catch (err) {
            // console.error('Error fetching user details:', err);
            // error(500, 'Failed to load user details');

            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
}
