<script lang="ts">
	import { enhance } from '$app/forms';
	import {
		P,
		AccordionItem,
		Accordion,
		Button,
		Hr,
		Dropdown,
		DropdownItem,
		Textarea,
		Select,
		Tooltip
	} from 'flowbite-svelte';
	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline
	} from 'flowbite-svelte-icons';
	import SummaryEditModal from './SummaryEditModal.svelte';
	import SummaryDeleteModal from './SummaryDeleteModal.svelte';

	export let ticket_summaries = [];
	export let ticketId;

	let editModal = false;
	let deleteModal = false;
	let editSummary;
	let deleteSummaryId;

	let selectedSentiment = 'Neutral';
	let sentimentOptions = [
		{ value: 'Positive', name: 'Positive' },
		{ value: 'Negative', name: 'Negative' },
		{ value: 'Neutral', name: 'Neutral' }
	];

	function formatTimestamp(timestamp) {
		const date = new Date(timestamp);
		const options = {
			day: '2-digit',
			month: 'short',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		};
		return date.toLocaleString('en-US', options).replace(',', '');
	}

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Add hour and minute in 24-hour format
		const hour = displayCreated.getHours().toString().padStart(2, '0');
		const minute = displayCreated.getMinutes().toString().padStart(2, '0');

		// Combine in desired format
		return `${day} ${month} ${year} ${hour}:${minute}`;
	};

	function openEditModal(summary) {
		editModal = true;
		editSummary = { ...summary };
	}

	function openDeletelModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	function closeDeleteModal() {
		deleteModal = false;
	}
</script>

<!-- <AccordionItem> -->
	<!-- <span slot="header" class="text-lg font-bold text-white">Summary</span> -->
	<form
		method="POST"
		enctype="multipart/form-data"
		action="?/upload_summary"
		use:enhance={() => {
			return async ({ update, result }) => {
				console.log(result);
				if (result.type === 'success') {
					await update();
					// showSuccess = true;
				} else if (result.type === 'failure') {
					// showError = false;
				}
			};
		}}
	>
		<div class="grid grid-cols-1 gap-4 md:grid-cols-[9fr_1fr]">
			<div>
				<Textarea rows="2" name="summary" placeholder="Type summary..." required />
				<Select
					class="mt-2"
					items={sentimentOptions}
					bind:value={selectedSentiment}
					placeholder="Select Sentiment"
				/>
				<input type="hidden" name="sentiment" value={selectedSentiment} />
			</div>
			<div>
				<Button
					type="submit"
					class="flex h-8 w-8 items-center justify-center rounded-full bg-sky-600 p-2 hover:bg-sky-700"
				>
					<PlusOutline class="h-4 w-4" />
				</Button>
				<input type="hidden" name="ticketId" value={ticketId} />
			</div>
		</div>
	</form>

	<Hr classHr="my-3" />
	<div class="custom-scrollbar max-h-80 overflow-y-auto">
		<ul>
			{#each ticket_summaries as summary (summary.created_on)}
				<li class="mb-4 border-b pb-4">
					<div class="flex flex-col space-y-1">
						<div class="grid grid-cols-1 gap-4 md:grid-cols-[8fr_1fr]">
							<div class="grid grid-cols-1 gap-4 md:grid-cols-[3fr_6fr_2fr]">
								<!-- User who created or updated summary -->
								<div class="rounded-md bg-gray-50 p-2">
									<p class="inline text-sm font-medium text-gray-700">
										{summary.updated_by
											? summary.updated_by.username
											: summary.created_by
												? summary.created_by.username
												: 'System'}
									</p>
								</div>

								<!-- Created or update date of the summary -->
								<div class="rounded-md bg-gray-50 p-2">
									<p class="inline text-sm text-gray-700">
										{summary.updated_on
											? displayDate(summary.updated_on) + ' (Edited)'
											: displayDate(summary.created_on)}
									</p>
								</div>

								<!-- Sentiment Display -->
								<Button color="none" class="p-0">
									<p
										class={summary.sentiment === 'Positive'
											? 'inline-block flex items-center rounded-md bg-green-200 p-2 text-sm text-green-700'
											: summary.sentiment === 'Negative'
												? 'inline-block flex items-center rounded-md bg-red-200 p-2 text-sm text-red-700'
												: summary.sentiment === 'Neutral'
													? 'inline-block flex items-center rounded-md bg-gray-100 p-2 text-sm text-gray-700'
													: 'inline-block flex items-center rounded-md bg-gray-100 p-2 text-sm text-gray-700'}
									>
										<img
											src={summary.sentiment === 'Positive'
												? '/images/sentiment-positive.png'
												: summary.sentiment === 'Negative'
													? '/images/sentiment-negative.png'
													: summary.sentiment === 'Neutral'
														? '/images/sentiment-neutral.png'
														: '/images/sentiment-neutral.png'}
											alt={summary.sentiment}
											class="h-5 w-5"
										/>
									</p>
								</Button>
								<Tooltip>{summary.sentiment}</Tooltip>
							</div>
							<div>
								<Button
									color="light"
									class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
								>
									<AngleDownOutline class="h-4 w-4" />
								</Button>

								<Dropdown>
									<DropdownItem
										class="flex items-center space-x-2"
										on:click={openEditModal(summary)}
									>
										Edit Summary <PenOutline class="h-4 w-4" />
									</DropdownItem>
									<DropdownItem
										class="flex items-center space-x-2"
										on:click={openDeletelModal(summary.id)}
									>
										Delete Summary <TrashBinOutline class="h-4 w-4" />
									</DropdownItem>
								</Dropdown>
							</div>
						</div>
						<p>
							<!--Chatbot Summary-->
							{#if summary.created_by.username === 'system'}
								<span>
									{#if summary['summary']?.includes('[SUB]') && summary['summary']?.includes('[VERB]') && summary['summary']?.includes('[OBJ]') && summary['summary']?.includes('[INTERLOCUTOR]')}
										<span class="border-black-500 border-b-2 text-black">
											{summary['summary'].split('[SUB]')[1]?.split('[VERB]')[0] || ''}
										</span>
										<span class="border-b-2 border-blue-500 text-black">
											{summary['summary'].split('[VERB]')[1]?.split('[OBJ]')[0] || ''}
										</span>
										<span class="border-b-2 border-blue-500 text-black">
											{summary['summary'].split('[OBJ]')[1]?.split('[INTERLOCUTOR]')[0] || ''}
										</span>
										<span class="border-black-500 border-b-2 text-black">
											{summary['summary'].split('[INTERLOCUTOR]')[1] || ''}
										</span>
									{:else}
										<span class="text-gray-700">{summary['summary']}</span>
									{/if}
								</span>
							{:else}
								{summary['summary']}
							{/if}
						</p>
					</div>
				</li>
			{/each}
		</ul>
	</div>
<!-- </AccordionItem> -->

<SummaryEditModal bind:editModal {editSummary} {ticketId} />
<SummaryDeleteModal bind:deleteModal {deleteSummaryId} {ticketId} on:close={closeDeleteModal} />

<style>
	/* Custom Scrollbar */
	.custom-scrollbar::-webkit-scrollbar {
		width: 8px; /* Thinner scrollbar */
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: #f1f1f1; /* Light track color */
		border-radius: 10px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background: #888; /* Thumb color */
		border-radius: 10px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background: #555; /* Thumb color on hover */
	}
</style>
