import { redirect, error, fail } from "@sveltejs/kit";
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { PUBLIC_BACKEND_URL } from "$env/static/private";
// import { env as publicEnv } from '$env/dynamic/public'; 
import { getBackendUrl } from '$src/lib/config';

import type { PageServerLoad } from './$types';
import type { Actions } from "@sveltejs/kit";
import { services } from "$lib/api/features";

export const load: PageServerLoad = async ({ params, cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    
    const ticketId = params.id;
    if (!access_token) {
        return {
            ticketId,
            ticket: [],
            ticket_owners_history: [],
            ticket_messages: [],
            // customer_policyholders: ,
            users: [],
            statuses: [],
            customer: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response_ticket = await services.tickets.getById(ticketId, access_token);
            const response_ticket_messages = await services.tickets.getTicketMessages(ticketId, access_token);
            const response_ticket_owners_history = await services.tickets.getTicketOwnersHistory(ticketId, access_token);
            const response_users = await services.users.getAll(access_token);
            const response_statuses = await services.statuses.getAll(access_token);
            const response_priorities = await services.ticket_priority.getAll(access_token);
            const response_ticket_owners = await services.tickets.getTicketOwners(ticketId, access_token);
            const response_ticket_summaries = await services.tickets.getAnalysis(ticketId, access_token);
            const response_ticket_topics = await services.tickets.getAllTicketTopics(access_token);
            const response_selfUserInfo = await services.users.getUserInfo(access_token);

            if (response_ticket.res_status === 401 || response_ticket_messages.res_status === 401 || response_ticket_owners_history.res_status === 401 || response_users.res_status === 401 || response_statuses.res_status === 401 || response_priorities.res_status === 401 || response_selfUserInfo.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            let customerId = response_ticket_messages.ticket_messages.customer_id;

            const response_customer_policyholders = await services.customers.getCustomerOwnPolicies(customerId, access_token);
            const response_customer_notes = await services.customers.getCustomerNotes(customerId, access_token);

            if (response_customer_policyholders.res_status === 401 || response_customer_notes === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            return {
                ticketId,
                ticket: response_ticket.tickets || [],
                // ticket_messages: response_ticket_messages.ticket_messages || [],
                ticket_messages: response_ticket_messages.ticket_messages.messages || [],
                // ticket_messages: ticket_messages || [],
                ticket_owners_history: response_ticket_owners_history.ticket_messages || [],
                // ticket_owners_history: ticket_owners || [],
                customer_policyholders: response_customer_policyholders.customer_policies || [],
                customer_notes: response_customer_notes.customer_notes || [],
                users: response_users.users || [],
                ticket_owners: response_ticket_owners.ticket_messages || [],
                statuses: response_statuses.statuses || [],
                access_token: access_token,
                ticket_summaries: response_ticket_summaries.tickets || [],
                ticket_topics: response_ticket_topics.ticket_topics || [],
                priorities: response_priorities.priorities || [],
                loginUser: response_selfUserInfo.users
            };
        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }

}

export const actions: Actions = {
    ticket_transfer_owner: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_owner_id = formData.get('new_owner_id');

        const url = `${getBackendUrl()}/ticket/ticket_transfer_owner/${ticket_id}/`

        console.log(`ticket_transfer_owner's formData - ${JSON.stringify({ formData })}`)
        console.log(`ticket_transfer_owner's ticket_id - ${ticket_id}`)
        console.log(`ticket_transfer_owner's new_owner_id - ${new_owner_id}`)

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_owner_id": new_owner_id
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            return { success: true };
        } catch (error) {
            console.error('Transfer Ticket Owner error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    change_ticket_status: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_status_id = formData.get('new_status_id');
        const new_ticket_topic = formData.get('new_ticket_topic');

        //When it close
        if (Number(formData.get('new_status_id')) === 3) { 

            // formData.new_ticket_topic = Number(new_ticket_topic)
            // console.log(new_status_id)
            // console.log(new_ticket_topic)
            // console.log(formData.new_ticket_topic)
            // console.log(typeof new_ticket_topic)

            // console.log(typeof formData.new_ticket_topic)
            const new_ticket_topic_array = new_ticket_topic.split(',').map(topic => Number(topic));
            // console.log(typeof new_ticket_topic_array)
            // console.log(new_ticket_topic_array)

            const url_ticket_topic = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/topics/`    
            try {
                const reponse_ticket_topic = await fetch(url_ticket_topic, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${access_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "topic_ids": new_ticket_topic_array
                    })
                });
                if (!reponse_ticket_topic.ok) {
                    const errorData = await reponse_ticket_topic.json();
                    // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                    throw new Error(`Status: ${errorData.message} (${reponse_ticket_topic.status})`);
                }
            } catch (error) {
                console.error('Change Ticket Topic error:', error);
                return fail(500, { error: `${error.message}` });
            }
        }

        
        const url = `${getBackendUrl()}/ticket/ticket_change_status/${ticket_id}/`
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_status_id": new_status_id
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            return { success: true };
        } catch (error) {
            console.error('Change Ticket Status error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    ticket_priority_change: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        const api_key = cookies.get('api_key'); // Assuming the token is stored in cookies
        const ticket_id = formData.get('ticket_id');
        const priority_id = formData.get('new_priority_id');

        
        const url = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/priority/`;

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json',
                    'X-API-Key': api_key
                },
                body: JSON.stringify({
                    "priority_id": priority_id
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }

            return { success: true };
        } catch (error) {
            console.error('Change Ticket Priority error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    upload_summary: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const summary = formData.get("summary");
        const ticketId = formData.get("ticketId");
        const sentiment = formData.get("sentiment");
        const bodyData = {
            "output": {
                "sentiment": sentiment,
                "summary": summary
            }
        };

        try {
            
            const url = `${getBackendUrl()}/ticket/api/tickets/${ticketId}/analyses/`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Summary Upload Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }

    },





    update_summary: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const summary = formData.get("summary");
        const sentiment = formData.get("sentiment");
        const ticketId = encodeURIComponent(formData.get("ticketId"));
        const ticketSummaryId = encodeURIComponent(formData.get("ticketSummaryId"));
        const bodyData = { summary: summary, sentiment: sentiment };

        try {
            
            const url = `${getBackendUrl()}/ticket/api/tickets/${ticketId}/analyses/${ticketSummaryId}/`;

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Summary Update Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    delete_summary: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const ticketId = encodeURIComponent(formData.get("ticketId"));
        const deleteSummaryId = encodeURIComponent(formData.get("ticketSummaryId"));

        try {
            
            const url = `${getBackendUrl()}/ticket/api/tickets/${ticketId}/analyses/${deleteSummaryId}/`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Summary Delete Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    upload_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = formData.get("customerId");
        const bodyData = { content };

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Upload Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    update_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = encodeURIComponent(formData.get("customerId"));
        const customerNoteId = encodeURIComponent(formData.get("customerNoteId"));
        const bodyData = { content };

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${customerNoteId}/`;

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Update Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    delete_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const customerId = encodeURIComponent(formData.get("customerId"));
        const deleteNoteId = encodeURIComponent(formData.get("deleteNoteId"));

        console.log(`customerId-monitoring - ${customerId}`);
        console.log(`deleteNoteId-monitoring - ${deleteNoteId}`);

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${deleteNoteId}/`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Delete Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },
};