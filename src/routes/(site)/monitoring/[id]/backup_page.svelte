<script lang="ts">
    import { Img, Card } from 'flowbite-svelte';
    import type { PageData } from './$types';
    import ChatMessages from './ChatMessages.svelte';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { HomeOutline, ChevronDoubleRightOutline, PaperPlaneSolid } from 'flowbite-svelte-icons';
    import AddNote from '$lib/components/UI/UserNote.svelte';

    export let data: PageData;
    $: ({   ticketId,
            ticket,
            ticket_messages,
            // ticket_owners_history,
            customer_policyholders, 
            users, 
            statuses, 
            customer
            // error 
    } = data);

    let ticketId = data.ticketId;
    let ticket = data.ticket;
    // let ticket_messages = data.ticket_messages.messages;
    let ticket_messages = data.ticket_messages;
    // let ticket_owners_history = data.ticket_owners_history.owner_history;
    let customer_policyholders = data.customer_policyholders;

    let pageTitle = `Monitoring/${ticketId}`;

    function formatDate(date: string): string {
        return new Date(date).toLocaleString(
            'en-GB', {
                hour12: false,
                weekday: 'long',
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
        });
    }
</script>

<svelte:head>
    <title>{pageTitle}</title>
</svelte:head>
<div class="max-w-[1200px] mx-auto p-6">
    <Breadcrumb aria-label="Default breadcrumb example">
        <BreadcrumbItem href="/" home>Home</BreadcrumbItem>
        <BreadcrumbItem href="/monitoring">Monitoring Table</BreadcrumbItem>
        <BreadcrumbItem>Ticket</BreadcrumbItem>
    </Breadcrumb>

    <!-- Header Section -->
    <div class="flex justify-between items-center mb-1">
        <h1 class="text-3xl font-bold text-gray-900">
            TC-{ticket.id} | {ticket.customer.name ? ticket.customer.name : ticket.customer.line_user.display_name}
        </h1>
        <div class="flex gap-3">
            <TransferTicketOwner {ticket} {users} />
            <ChangeTicketStatus {ticket} {statuses} />
        </div>
    </div>

    <!-- New Layout Grid -->
    <div class="grid grid-cols-12 gap-6">
        <!-- Ticket Information - Full Width Top -->
        <div class="col-span-12 bg-white rounded-lg p-6 shadow">
            <!-- <div class="bg-white rounded-lg p-6 shadow mb-6"> -->
                <div class="flex items-center gap-8"> <!-- Changed to flex and horizontal layout -->
                    <!-- <p><span class="font-semibold">Status:</span> {ticket.status}</p> -->
                    <p>
                        <span class="font-semibold">Status:</span> 
                        <span class={
                            ticket.status === 'close' ? 'inline-block bg-gray-100 text-gray-700 px-2 py-1 text-sm rounded-md' :
                            // ticket.status === 'waiting to be assigned' ? 'inline-block bg-yellow-200 text-yellow-700 px-2 py-1 text-sm rounded-md' :
                            ticket.status === 'waiting ' ? 'inline-block bg-yellow-200 text-yellow-700 px-2 py-1 text-sm rounded-md' :
                            ticket.status === 'open' ? 'inline-block bg-green-200 text-green-700 px-2 py-1 text-sm rounded-md' :
                            ticket.status === 'assigned' ? 'inline-block bg-blue-200 text-blue-700 px-2 py-1 text-sm rounded-md' :
                            'inline-block bg-gray-100 text-gray-700 px-2 py-1 text-sm rounded-md'
                        }>
                            {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                        </span>
                    </p>

                    <p><span class="font-semibold">Owner:</span> {ticket.owner.name}</p>
                    <p><span class="font-semibold">Message Intends:</span> {ticket.message_intends}</p>
                    <p><span class="font-semibold">Created:</span> {formatDate(ticket.created_on)}</p>
                    <p><span class="font-semibold">Updated:</span> {formatDate(ticket.updated_on)}</p>
                </div>
            <!-- </div> -->
        </div>

        <!-- Chat History - Left Side -->
        <div class="col-span-8 bg-white rounded-lg shadow">
            <div class="border-b px-6 py-3">
                <h3 class="font-semibold">Chat History</h3>
            </div>
            <div class="p-6">
                <ChatMessages messages={ticket_messages} />
            </div>
            <!-- Chat Input -->
            <div class="border-t p-4">
                <div class="flex items-center gap-2">
                    <input 
                        type="text" 
                        class="w-full rounded-full border border-gray-300 px-4 py-2"
                        placeholder="Type a message..."
                    />
                    <button class="text-gray-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Right Side Stack -->
        <div class="col-span-4 space-y-6">
            <!-- Customer Information -->
            <div class="bg-white rounded-lg p-6 shadow">
                <div class="flex items-start gap-4 mb-6">
                    <img 
                        src="/images/person-logo.png"
                        alt="Profile"
                        class="w-16 h-16 rounded-full border-2 border-purple-500"
                    />
                    <div class="space-y-1">
                        <!-- TODO - These values should have in case null -->
                        <p><span class="font-semibold">Age:</span> {ticket.customer.age}</p>
                        <p><span class="font-semibold">Gender:</span> {ticket.customer.gender}</p>
                        <p><span class="font-semibold">Phone:</span> {ticket.customer.phone}</p>
                        <p><span class="font-semibold">Email:</span> {ticket.customer.email}</p>
                        <!-- TODO - Change to Ticket's interface (FK) -->
                        <p><span class="font-semibold">Interface:</span> {ticket.customer.main_interface.name}</p>
                    </div>
                </div>
                <!-- Open this when a user have customer's own policies -->
                <!-- {#if customer_policyholders.length >= 1}
                    <div class="border-t pt-4">
                        <h3 class="font-semibold mb-2">Own Policy Product:</h3>
                        {#each customer_policyholders as policy}
                            <div class="flex items-center gap-2 mb-2">
                                <span>{policy.product.name}</span>
                                {#if policy.policy_status === 'expiring'}
                                    <span class="bg-red-500 text-white text-sm px-2 py-1 rounded">
                                        Expiring
                                    </span>
                                {/if}
                            </div>
                        {/each}
                    </div>
                {/if} -->
            </div>

            <!-- Note Section -->
            <div class="bg-white rounded-lg p-6 shadow">
                <!-- <h2 class="text-xl font-semibold mb-4">Note</h2> -->
                <AddNote {customer} />
            </div>

            <!-- Summary Section -->
            <div class="bg-white rounded-lg p-6 shadow">
                <h2 class="text-xl font-semibold mb-4">Summary</h2>
                <p class="text-gray-700">
                    {ticket.summary ? ticket.summary : 'There is no summarization yet.'}
                </p>
            </div>
        </div>
    </div>
</div>